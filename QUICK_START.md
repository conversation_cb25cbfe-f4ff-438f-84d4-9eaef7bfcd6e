# دليل البدء السريع - Asphalto

## 🚀 التثبيت والتشغيل

### 1. تثبيت Python
تأكد من تثبيت Python 3.8 أو أحدث:
```bash
python --version
```

### 2. تثبيت المتطلبات
```bash
# الطريقة الأولى: استخدام الملف المساعد
python install_requirements.py

# الطريقة الثانية: استخدام pip مباشرة
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
# الطريقة الأولى
python main.py

# الطريقة الثانية
python run_asphalto.py
```

## 🧪 اختبار التطبيق

### اختبار النواة الحسابية
```bash
python test_basic.py
```

### اختبار سريع للحساب
```python
from src.core.aashto_calculator import AASHTOCalculator, DesignInputs

# إنشاء مدخلات اختبارية
inputs = DesignInputs(
    w18=1000000,           # مليون محور مكافئ
    reliability=95.0,      # اعتمادية 95%
    serviceability_loss=2.0, # فقدان خدمة 2.0
    standard_deviation=0.45, # انحراف معياري 0.45
    design_period=20,      # فترة تصميم 20 سنة
    surface_modulus=450000,
    binder_modulus=400000,
    base_modulus=30000,
    subgrade_cbr=6.0       # CBR 6%
)

# تشغيل الحساب
calculator = AASHTOCalculator()
results = calculator.calculate(inputs)

# عرض النتائج
print(f"الرقم الهيكلي: {results.structural_number:.3f}")
print(f"السماكة الكلية: {results.layer_thickness.total:.1f} سم")
```

## 📋 استخدام التطبيق

### 1. إنشاء مشروع جديد
- افتح التطبيق
- اختر "ملف" > "مشروع جديد"
- أدخل اسم المشروع ووصفه

### 2. إدخال البيانات

#### معاملات الحركة المرورية
- **W18**: عدد المحاور المكافئة (مثال: 1,000,000)
- **فئة الحركة**: اختر من القائمة المنسدلة

#### معاملات الاعتمادية
- **الاعتمادية**: 80-99% (مُوصى: 95%)
- **الانحراف المعياري**: 0.35-0.50 (افتراضي: 0.45)
- **فقدان الخدمة**: 1.5-2.5 (افتراضي: 2.0)

#### خصائص المواد
- **الطبقة السطحية**: اختر نوع الخلطة الإسفلتية
- **الطبقة الرابطة**: اختر نوع المادة الرابطة
- **طبقة الأساس**: اختر نوع مادة الأساس

#### خصائص التربة
- **نوع التربة**: اختر من القائمة
- **CBR**: قيمة نسبة تحمل كاليفورنيا (2-30%)

### 3. تشغيل الحساب
- اضغط على زر "حساب سماكة الطبقات"
- راجع النتائج في تبويب "النتائج"

### 4. تحليل النتائج
- انتقل إلى تبويب "التحليل"
- اختر نوع التحليل المطلوب:
  - تحليل الحساسية
  - التقييم الزمني
  - مقارنة التصميمات

### 5. تصدير التقرير
- اختر "ملف" > "تصدير التقرير"
- حدد تنسيق التصدير (PDF مُوصى)

## 🔧 حل المشاكل الشائعة

### خطأ "No module named 'PyQt6'"
```bash
pip install PyQt6
```

### خطأ في عرض الخطوط العربية
- تأكد من وجود خطوط عربية في النظام
- جرب تغيير الخط في الإعدادات

### بطء في التشغيل
- أغلق التطبيقات الأخرى
- قلل دقة الرسوم في الإعدادات

### خطأ في حفظ المشروع
- تأكد من صلاحيات الكتابة في مجلد المشاريع
- جرب تشغيل التطبيق كمدير

## 📊 أمثلة سريعة

### طريق سكني
- W18: 100,000
- الاعتمادية: 85%
- CBR: 8%
- النتيجة المتوقعة: ~25 سم

### طريق رئيسي
- W18: 3,000,000
- الاعتمادية: 90%
- CBR: 6%
- النتيجة المتوقعة: ~35 سم

### طريق سريع
- W18: 10,000,000
- الاعتمادية: 95%
- CBR: 5%
- النتيجة المتوقعة: ~45 سم

## 📞 الدعم الفني

**المطور**: المهندس محمد يونس الجوالي
- **WhatsApp**: +964 750 781 2241
- **Facebook**: facebook.com/aljwalei
- **Instagram**: instagram.com/m2n.9

## 🔄 التحديثات

للحصول على آخر التحديثات:
1. تابع صفحة المطور على فيسبوك
2. راجع ملف README.md
3. تحقق من الإعدادات > حول البرنامج

---

**نصائح مهمة:**
- احفظ مشروعك بانتظام
- استخدم الوضع التعليمي للتعلم
- راجع التحذيرات في النتائج
- اختبر النتائج مع حسابات يدوية

🎯 **هدفنا**: توفير أداة دقيقة وسهلة لمهندسي الطرق العرب
