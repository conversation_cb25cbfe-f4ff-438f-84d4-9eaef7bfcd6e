#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration management for Asphalto application
"""

import os
import json
from typing import Dict, Any, Optional
from PyQt6.QtCore import QSettings, QStandardPaths

class Config:
    """Application configuration manager"""
    
    def __init__(self):
        """Initialize configuration"""
        self.settings = QSettings()
        self.app_data_dir = QStandardPaths.writableLocation(
            QStandardPaths.StandardLocation.AppDataLocation
        )
        
        # Ensure app data directory exists
        os.makedirs(self.app_data_dir, exist_ok=True)
        
        # Default configuration
        self.defaults = {
            'language': 'ar',  # Default to Arabic
            'theme': 'light',  # light or dark
            'auto_save': True,
            'backup_count': 5,
            'units': 'metric',  # metric or imperial
            'precision': 2,  # decimal places
            'show_tooltips': True,
            'show_educational_mode': True,
            'default_reliability': 95.0,
            'default_serviceability_loss': 2.0,
            'default_standard_deviation': 0.45,
            'default_design_period': 20,
            'window_geometry': None,
            'window_state': None,
            'recent_projects': [],
            'material_library_path': None
        }
        
        # Load user settings
        self._load_settings()
    
    def _load_settings(self):
        """Load settings from QSettings"""
        for key, default_value in self.defaults.items():
            if not self.settings.contains(key):
                self.settings.setValue(key, default_value)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        if default is None:
            default = self.defaults.get(key)
        return self.settings.value(key, default)
    
    def set(self, key: str, value: Any):
        """Set configuration value"""
        self.settings.setValue(key, value)
        self.settings.sync()
    
    def get_language(self) -> str:
        """Get current language"""
        return self.get('language', 'ar')
    
    def set_language(self, language: str):
        """Set current language"""
        self.set('language', language)
    
    def get_theme(self) -> str:
        """Get current theme"""
        return self.get('theme', 'light')
    
    def set_theme(self, theme: str):
        """Set current theme"""
        self.set('theme', theme)
    
    def get_app_data_dir(self) -> str:
        """Get application data directory"""
        return self.app_data_dir
    
    def get_projects_dir(self) -> str:
        """Get projects directory"""
        projects_dir = os.path.join(self.app_data_dir, 'projects')
        os.makedirs(projects_dir, exist_ok=True)
        return projects_dir
    
    def get_backups_dir(self) -> str:
        """Get backups directory"""
        backups_dir = os.path.join(self.app_data_dir, 'backups')
        os.makedirs(backups_dir, exist_ok=True)
        return backups_dir
    
    def get_material_library_path(self) -> str:
        """Get material library path"""
        custom_path = self.get('material_library_path')
        if custom_path and os.path.exists(custom_path):
            return custom_path
        
        # Default material library path
        default_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), 
            'data', 
            'material_library.json'
        )
        return default_path
    
    def add_recent_project(self, project_path: str):
        """Add project to recent projects list"""
        recent = self.get('recent_projects', [])
        if isinstance(recent, str):
            recent = []
        
        # Remove if already exists
        if project_path in recent:
            recent.remove(project_path)
        
        # Add to beginning
        recent.insert(0, project_path)
        
        # Keep only last 10
        recent = recent[:10]
        
        self.set('recent_projects', recent)
    
    def get_recent_projects(self) -> list:
        """Get recent projects list"""
        recent = self.get('recent_projects', [])
        if isinstance(recent, str):
            return []
        
        # Filter out non-existent files
        existing = [p for p in recent if os.path.exists(p)]
        if len(existing) != len(recent):
            self.set('recent_projects', existing)
        
        return existing
    
    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        self.settings.clear()
        self._load_settings()
    
    def export_settings(self, file_path: str) -> bool:
        """Export settings to file"""
        try:
            settings_dict = {}
            for key in self.defaults.keys():
                settings_dict[key] = self.get(key)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(settings_dict, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"Error exporting settings: {e}")
            return False
    
    def import_settings(self, file_path: str) -> bool:
        """Import settings from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                settings_dict = json.load(f)
            
            for key, value in settings_dict.items():
                if key in self.defaults:
                    self.set(key, value)
            
            return True
        except Exception as e:
            print(f"Error importing settings: {e}")
            return False
