{"name": "asphalto", "private": true, "version": "1.0.0", "main": "electron/main.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "electron:dev": "concurrently \"vite\" \"electron .\"", "electron:build": "vite build && electron-builder", "test": "jest"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.3", "@react-three/drei": "^9.80.1", "@react-three/fiber": "^8.13.6", "chart.js": "^4.3.3", "electron-is-dev": "^2.0.0", "i18next": "^23.4.4", "pdf-lib": "^1.17.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.0.3", "recharts": "^2.7.2", "three": "^0.155.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "concurrently": "^8.2.0", "electron": "^25.3.2", "electron-builder": "^24.6.3", "typescript": "^5.0.2", "vite": "^4.4.5", "wait-on": "^7.0.1"}}