# Asphalto - Professional Asphalt Pavement Thickness Calculator

## 💠 **الوصف الكامل لتطبيق احترافي لحساب سماكة طبقات الرصف الإسفلتي**

تطبيق عصري، احترافي، وسهل الاستخدام يُستخدم لحساب سماكة طبقات الرصف الإسفلتي بطريقة ذكية وفق نظام **AASHTO 1993**، مع دعم الميزات التعليمية، التحليلية، والمحاكاة المتقدمة.

## 🚀 الميزات الرئيسية

### ⚙️ الوظائف الأساسية
- حساب دقيق لسماكة الطبقات وفق معادلة AASHTO 1993
- توزيع الرقم الهيكلي (SN) على ثلاث طبقات رئيسية
- مكتبة شاملة للمواد والترب مع إمكانية التحديث
- نظام التحقق الذكي من صحة الإدخالات

### 📈 التحليلات المتقدمة
- **تحليل الحساسية**: دراسة تأثير تغير المعاملات على النتائج
- **التقييم الزمني**: تحليل أداء الطريق عبر فترة التصميم
- **مقارنة التصميمات**: مقارنة سيناريوهات مختلفة
- **تحليل الأداء**: محاكاة تدهور الطريق مع الزمن

### 🧠 الميزات الذكية
- نظام توصيات ذكي لاختيار المواد المناسبة
- التحقق التلقائي من منطقية القيم المدخلة
- النسخ الاحتياطي التلقائي للمشاريع
- نظام إدارة المشاريع المتكامل

### 🎨 واجهة المستخدم العصرية
- تصميم حديث مع دعم الوضع الداكن
- دعم كامل للغة العربية والإنجليزية
- واجهة تفاعلية مع رسوميات متحركة
- نظام مساعدة تعليمي مدمج

## 🛠️ متطلبات التشغيل

### متطلبات النظام
- **نظام التشغيل**: Windows 10/11, Linux, macOS
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 4 GB RAM (8 GB مُوصى به)
- **مساحة القرص**: 500 MB

### المكتبات المطلوبة
```
PyQt6==6.6.1
matplotlib==3.8.2
plotly==5.17.0
numpy==1.26.2
scipy==1.11.4
pandas==2.1.4
reportlab==4.0.7
Pillow==10.1.0
pyqtgraph==0.13.3
kaleido==0.2.1
openpyxl==3.1.2
xlsxwriter==3.1.9
pyinstaller==6.3.0
```

## 📦 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone https://github.com/username/asphalto.git
cd asphalto
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python main.py
```

أو استخدام الملف المساعد:
```bash
python run_asphalto.py
```

## 📁 هيكل المشروع

```
asphalto/
├── main.py                 # نقطة دخول التطبيق
├── run_asphalto.py         # ملف تشغيل مساعد
├── requirements.txt        # متطلبات Python
├── README.md              # هذا الملف
├── src/                   # كود المصدر الرئيسي
│   ├── core/              # المحرك الحسابي
│   │   ├── aashto_calculator.py
│   │   └── config.py
│   ├── ui/                # واجهة المستخدم
│   │   ├── main_window.py
│   │   ├── input_panel.py
│   │   ├── results_panel.py
│   │   ├── analysis_panel.py
│   │   ├── settings_dialog.py
│   │   └── about_dialog.py
│   ├── data/              # إدارة البيانات
│   │   ├── material_manager.py
│   │   ├── project_manager.py
│   │   └── material_library.json
│   └── utils/             # أدوات مساعدة
├── assets/                # الموارد
│   ├── icons/
│   └── images/
├── translations/          # ملفات الترجمة
├── tests/                 # اختبارات الوحدة
└── docs/                  # التوثيق
```

## 🎯 كيفية الاستخدام

### 1. إنشاء مشروع جديد
- افتح التطبيق
- اختر "ملف" > "مشروع جديد"
- أدخل معلومات المشروع

### 2. إدخال معاملات التصميم
- **معاملات الحركة المرورية**: عدد المحاور المكافئة (W18)
- **معاملات الاعتمادية**: مستوى الاعتمادية والانحراف المعياري
- **خصائص المواد**: اختيار مواد الطبقات ومعاملاتها
- **خصائص التربة**: نوع التربة وقيمة CBR

### 3. تشغيل الحساب
- اضغط على زر "حساب سماكة الطبقات"
- راجع النتائج في تبويب "النتائج"
- استكشف التحليلات في تبويب "التحليل"

### 4. تصدير التقرير
- اختر "ملف" > "تصدير التقرير"
- حدد تنسيق التصدير (PDF, Excel, Word)
- احفظ التقرير في الموقع المطلوب

## 🔧 الإعدادات والتخصيص

### إعدادات عامة
- تغيير اللغة (عربي/إنجليزي)
- اختيار المظهر (فاتح/داكن)
- تفعيل الحفظ التلقائي

### إعدادات الحسابات
- القيم الافتراضية للمعاملات
- دقة الحسابات
- نظام الوحدات

### إعدادات العرض
- حجم ونوع الخط
- نمط الرسوم البيانية
- عرض التلميحات والمساعدة

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
python -m pytest tests/
```

### اختبار يدوي
1. تشغيل التطبيق
2. إنشاء مشروع تجريبي
3. إدخال قيم اختبارية
4. التحقق من صحة النتائج

## 📊 أمثلة الاستخدام

### مثال 1: طريق سكني
- W18: 100,000 ESAL
- الاعتمادية: 85%
- CBR: 8%
- النتيجة المتوقعة: ~25 سم

### مثال 2: طريق سريع
- W18: 10,000,000 ESAL
- الاعتمادية: 95%
- CBR: 6%
- النتيجة المتوقعة: ~45 سم

## 🐛 الأخطاء الشائعة وحلولها

### خطأ في تثبيت PyQt6
```bash
pip install PyQt6 --upgrade
```

### خطأ في عرض الخطوط العربية
- تأكد من وجود خطوط عربية في النظام
- استخدم خط "Arial Unicode MS" أو "Tahoma"

### بطء في الرسوم البيانية
- قلل دقة الرسوم في الإعدادات
- أغلق التطبيقات الأخرى المستهلكة للذاكرة

## 🤝 المساهمة في التطوير

نرحب بمساهماتكم في تطوير البرنامج:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. تطوير واختبار التحسينات
4. إرسال Pull Request

## 📞 التواصل والدعم

**المطور**: المهندس محمد يونس الجوالي

- **WhatsApp**: [+964 750 781 2241](https://wa.me/9647507812241)
- **Facebook**: [facebook.com/aljwalei](https://facebook.com/aljwalei)
- **Instagram**: [instagram.com/m2n.9](https://instagram.com/m2n.9)

## 📄 الترخيص

جميع الحقوق محفوظة © 2025 المهندس محمد يونس الجوالي

هذا البرنامج مُرخص للاستخدام التعليمي والتجاري مع الحفاظ على حقوق المطور.

## 🙏 شكر وتقدير

- **AASHTO** لتطوير معايير التصميم
- **مجتمع Python** لتوفير أدوات التطوير
- **المهندسين المختبرين** لملاحظاتهم القيمة

---

**تم تطوير هذا البرنامج بعناية فائقة لخدمة مجتمع المهندسين العرب** 🏗️
