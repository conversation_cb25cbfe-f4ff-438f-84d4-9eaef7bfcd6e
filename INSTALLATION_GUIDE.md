# دليل التثبيت والتشغيل - Asphalto

## 📋 متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10, Linux Ubuntu 18.04+, macOS 10.14+
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 4 GB RAM
- **مساحة القرص**: 500 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 أو أعلى

### المُوصى به
- **نظام التشغيل**: Windows 11, Linux Ubuntu 20.04+, macOS 11+
- **Python**: 3.10 أو أحدث
- **الذاكرة**: 8 GB RAM
- **مساحة القرص**: 1 GB مساحة فارغة
- **الشاشة**: دقة 1920x1080 أو أعلى

## 🔧 خطوات التثبيت

### الخطوة 1: تثبيت Python

#### Windows:
1. تحميل Python من [python.org](https://python.org)
2. تشغيل المثبت مع تحديد "Add Python to PATH"
3. التحقق من التثبيت:
```cmd
python --version
pip --version
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

#### macOS:
```bash
# باستخدام Homebrew
brew install python3

# أو تحميل من python.org
```

### الخطوة 2: تحميل Asphalto

#### الطريقة الأولى: Git Clone
```bash
git clone https://github.com/aljwalei/asphalto.git
cd asphalto
```

#### الطريقة الثانية: تحميل ZIP
1. تحميل الملف المضغوط من GitHub
2. استخراج الملفات إلى مجلد
3. فتح موجه الأوامر في المجلد

### الخطوة 3: إنشاء بيئة افتراضية (مُوصى)

```bash
# إنشاء بيئة افتراضية
python -m venv asphalto_env

# تفعيل البيئة الافتراضية
# Windows:
asphalto_env\Scripts\activate

# Linux/macOS:
source asphalto_env/bin/activate
```

### الخطوة 4: تثبيت المتطلبات

#### الطريقة الأولى: استخدام الملف المساعد
```bash
python install_requirements.py
```

#### الطريقة الثانية: استخدام pip مباشرة
```bash
pip install -r requirements.txt
```

#### الطريقة الثالثة: تثبيت يدوي
```bash
pip install PyQt6==6.6.1
pip install matplotlib==3.8.2
pip install numpy==1.26.2
pip install scipy==1.11.4
pip install pandas==2.1.4
pip install reportlab==4.0.7
pip install Pillow==10.1.0
pip install openpyxl==3.1.2
```

### الخطوة 5: اختبار التثبيت

```bash
# اختبار النواة الحسابية
python test_basic.py

# اختبار التطبيق الكامل
python main.py
```

## 🚀 تشغيل التطبيق

### التشغيل العادي
```bash
python main.py
```

### التشغيل باستخدام الملف المساعد
```bash
python run_asphalto.py
```

### التشغيل مع معاملات إضافية
```bash
# تشغيل بوضع التطوير
python main.py --debug

# تشغيل بلغة محددة
python main.py --language ar
```

## 🔨 إنشاء ملف تنفيذي

### للتوزيع
```bash
# تثبيت PyInstaller
pip install pyinstaller

# إنشاء الملف التنفيذي
python build_executable.py
```

### الملف التنفيذي سيكون في:
- Windows: `dist/Asphalto.exe`
- Linux: `dist/Asphalto`
- macOS: `dist/Asphalto.app`

## 🐛 حل المشاكل الشائعة

### مشكلة: "No module named 'PyQt6'"
```bash
# الحل
pip install PyQt6 --upgrade
```

### مشكلة: خطأ في matplotlib
```bash
# الحل
pip install matplotlib --upgrade
# أو
pip install matplotlib --force-reinstall
```

### مشكلة: خطأ في الخطوط العربية
1. تأكد من وجود خطوط عربية في النظام
2. جرب تغيير الخط في إعدادات التطبيق
3. في Linux، ثبت الخطوط العربية:
```bash
sudo apt install fonts-arabeyes fonts-kacst
```

### مشكلة: بطء في التشغيل
1. أغلق التطبيقات الأخرى
2. قلل دقة الرسوم في الإعدادات
3. تأكد من وجود ذاكرة كافية

### مشكلة: خطأ في الصلاحيات
```bash
# Windows: تشغيل كمدير
# Linux/macOS:
sudo python main.py
```

## 📁 هيكل الملفات بعد التثبيت

```
asphalto/
├── main.py                    # ملف التشغيل الرئيسي
├── run_asphalto.py           # ملف تشغيل مساعد
├── requirements.txt          # متطلبات Python
├── install_requirements.py   # مثبت المتطلبات
├── test_basic.py            # اختبارات أساسية
├── build_executable.py      # منشئ الملف التنفيذي
├── README.md                # دليل المشروع
├── QUICK_START.md           # دليل البدء السريع
├── INSTALLATION_GUIDE.md    # هذا الملف
├── src/                     # كود المصدر
│   ├── core/               # النواة الحسابية
│   ├── ui/                 # واجهة المستخدم
│   ├── data/               # إدارة البيانات
│   └── utils/              # أدوات مساعدة
├── assets/                 # الموارد
├── translations/           # ملفات الترجمة
├── tests/                  # الاختبارات
└── docs/                   # التوثيق
```

## 🔄 التحديث

### تحديث التطبيق
```bash
# إذا كان مثبت من Git
git pull origin main
pip install -r requirements.txt

# إذا كان مثبت من ZIP
# حمل النسخة الجديدة واستبدل الملفات
```

### تحديث المتطلبات فقط
```bash
pip install -r requirements.txt --upgrade
```

## 📞 الدعم الفني

### في حالة مواجهة مشاكل:

1. **تحقق من السجلات**:
   - Windows: `%APPDATA%/Asphalto/logs/`
   - Linux: `~/.local/share/Asphalto/logs/`
   - macOS: `~/Library/Application Support/Asphalto/logs/`

2. **تشغيل الاختبارات**:
   ```bash
   python test_basic.py
   ```

3. **التواصل مع المطور**:
   - **WhatsApp**: +964 750 781 2241
   - **Facebook**: facebook.com/aljwalei
   - **Instagram**: instagram.com/m2n.9

### معلومات مفيدة للدعم:
- إصدار Python: `python --version`
- إصدار النظام: `uname -a` (Linux/macOS) أو `systeminfo` (Windows)
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

## ✅ التحقق من نجاح التثبيت

### قائمة التحقق:
- [ ] Python مثبت ويعمل
- [ ] جميع المتطلبات مثبتة
- [ ] اختبار test_basic.py يمر بنجاح
- [ ] التطبيق يفتح بدون أخطاء
- [ ] يمكن إنشاء مشروع جديد
- [ ] يمكن تشغيل حساب تجريبي
- [ ] يمكن حفظ وفتح المشاريع

### إذا نجحت جميع النقاط:
🎉 **تهانينا! تم تثبيت Asphalto بنجاح**

يمكنك الآن البدء في استخدام التطبيق لتصميم الرصف الإسفلتي.

---

**ملاحظة**: هذا الدليل يُحدث باستمرار. للحصول على آخر إصدار، راجع الملف على GitHub أو تواصل مع المطور.
