#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Input Panel for Asphalto Application
Handles user input for pavement design parameters
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QGroupBox, QLineEdit, QDoubleSpinBox, QSpinBox,
                            QComboBox, QLabel, QPushButton, QTextEdit,
                            QCheckBox, QSlider, QFrame, QScrollArea,
                            QToolButton, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QPixmap

from src.core.aashto_calculator import DesignInputs
from src.data.material_manager import MaterialManager
from src.core.config import Config

class InputPanel(QWidget):
    """Input panel for design parameters"""
    
    # Signals
    calculate_requested = pyqtSignal()
    inputs_changed = pyqtSignal()
    
    def __init__(self, material_manager: MaterialManager, config: Config):
        super().__init__()
        
        self.material_manager = material_manager
        self.config = config
        self.current_project = None
        
        # Input change timer (for delayed signal emission)
        self.change_timer = QTimer()
        self.change_timer.setSingleShot(True)
        self.change_timer.timeout.connect(self.inputs_changed.emit)
        
        self.init_ui()
        self.setup_connections()
        self.load_default_values()
    
    def init_ui(self):
        """Initialize user interface"""
        # Create scroll area for the input panel
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # Main widget inside scroll area
        main_widget = QWidget()
        scroll.setWidget(main_widget)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll)
        
        # Content layout
        layout = QVBoxLayout(main_widget)
        layout.setSpacing(10)
        
        # Title
        title_label = QLabel("معاملات التصميم")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        layout.addWidget(title_label)
        
        # Traffic parameters group
        self.create_traffic_group(layout)
        
        # Reliability parameters group
        self.create_reliability_group(layout)
        
        # Material properties group
        self.create_materials_group(layout)
        
        # Subgrade properties group
        self.create_subgrade_group(layout)
        
        # Design parameters group
        self.create_design_group(layout)
        
        # Calculate button
        self.create_calculate_button(layout)
        
        # Add stretch to push everything to top
        layout.addStretch()
    
    def create_traffic_group(self, parent_layout):
        """Create traffic parameters group"""
        group = QGroupBox("معاملات الحركة المرورية")
        group.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout = QFormLayout(group)
        
        # W18 input
        w18_layout = QHBoxLayout()
        self.w18_input = QDoubleSpinBox()
        self.w18_input.setRange(1000, 1e9)
        self.w18_input.setDecimals(0)
        self.w18_input.setValue(1000000)
        self.w18_input.setSuffix(" ESAL")
        w18_layout.addWidget(self.w18_input)
        
        # Help button for W18
        w18_help = QToolButton()
        w18_help.setText("؟")
        w18_help.setToolTip("عدد المحاور المكافئة 18-kip خلال فترة التصميم")
        w18_help.clicked.connect(lambda: self.show_help("W18"))
        w18_layout.addWidget(w18_help)
        
        layout.addRow("عدد المحاور المكافئة (W18):", w18_layout)
        
        # Traffic category selector
        self.traffic_category = QComboBox()
        self.load_traffic_categories()
        self.traffic_category.currentTextChanged.connect(self.on_traffic_category_changed)
        layout.addRow("فئة الحركة المرورية:", self.traffic_category)
        
        parent_layout.addWidget(group)
    
    def create_reliability_group(self, parent_layout):
        """Create reliability parameters group"""
        group = QGroupBox("معاملات الاعتمادية")
        group.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout = QFormLayout(group)
        
        # Reliability input
        reliability_layout = QHBoxLayout()
        self.reliability_input = QDoubleSpinBox()
        self.reliability_input.setRange(50, 99.9)
        self.reliability_input.setDecimals(1)
        self.reliability_input.setValue(95.0)
        self.reliability_input.setSuffix(" %")
        reliability_layout.addWidget(self.reliability_input)
        
        # Reliability slider
        self.reliability_slider = QSlider(Qt.Orientation.Horizontal)
        self.reliability_slider.setRange(500, 999)
        self.reliability_slider.setValue(950)
        self.reliability_slider.valueChanged.connect(
            lambda v: self.reliability_input.setValue(v / 10.0)
        )
        reliability_layout.addWidget(self.reliability_slider)
        
        # Help button
        rel_help = QToolButton()
        rel_help.setText("؟")
        rel_help.setToolTip("مستوى الاعتمادية المطلوب للتصميم")
        rel_help.clicked.connect(lambda: self.show_help("Reliability"))
        reliability_layout.addWidget(rel_help)
        
        layout.addRow("الاعتمادية (R):", reliability_layout)
        
        # Standard deviation
        std_layout = QHBoxLayout()
        self.std_input = QDoubleSpinBox()
        self.std_input.setRange(0.1, 1.0)
        self.std_input.setDecimals(2)
        self.std_input.setValue(0.45)
        std_layout.addWidget(self.std_input)
        
        std_help = QToolButton()
        std_help.setText("؟")
        std_help.setToolTip("الانحراف المعياري الإجمالي")
        std_help.clicked.connect(lambda: self.show_help("StandardDeviation"))
        std_layout.addWidget(std_help)
        
        layout.addRow("الانحراف المعياري (S0):", std_layout)
        
        # Serviceability loss
        psi_layout = QHBoxLayout()
        self.psi_input = QDoubleSpinBox()
        self.psi_input.setRange(1.0, 4.0)
        self.psi_input.setDecimals(1)
        self.psi_input.setValue(2.0)
        psi_layout.addWidget(self.psi_input)
        
        psi_help = QToolButton()
        psi_help.setText("؟")
        psi_help.setToolTip("الفرق بين مؤشر الخدمة الأولي والنهائي")
        psi_help.clicked.connect(lambda: self.show_help("Serviceability"))
        psi_layout.addWidget(psi_help)
        
        layout.addRow("فقدان الخدمة (ΔPSI):", psi_layout)
        
        parent_layout.addWidget(group)
    
    def create_materials_group(self, parent_layout):
        """Create material properties group"""
        group = QGroupBox("خصائص المواد")
        group.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout = QFormLayout(group)
        
        # Surface course material
        self.surface_material = QComboBox()
        self.load_materials('surface')
        self.surface_material.currentTextChanged.connect(self.on_surface_material_changed)
        layout.addRow("مادة الطبقة السطحية:", self.surface_material)
        
        # Surface coefficient
        surface_coeff_layout = QHBoxLayout()
        self.surface_coeff = QDoubleSpinBox()
        self.surface_coeff.setRange(0.1, 0.6)
        self.surface_coeff.setDecimals(3)
        self.surface_coeff.setValue(0.44)
        surface_coeff_layout.addWidget(self.surface_coeff)
        
        surface_coeff_help = QToolButton()
        surface_coeff_help.setText("؟")
        surface_coeff_help.setToolTip("معامل الطبقة السطحية (a1)")
        surface_coeff_help.clicked.connect(lambda: self.show_help("SurfaceCoeff"))
        surface_coeff_layout.addWidget(surface_coeff_help)
        
        layout.addRow("معامل الطبقة السطحية (a1):", surface_coeff_layout)
        
        # Binder course material
        self.binder_material = QComboBox()
        self.load_materials('binder')
        self.binder_material.currentTextChanged.connect(self.on_binder_material_changed)
        layout.addRow("مادة الطبقة الرابطة:", self.binder_material)
        
        # Binder coefficient
        binder_coeff_layout = QHBoxLayout()
        self.binder_coeff = QDoubleSpinBox()
        self.binder_coeff.setRange(0.1, 0.6)
        self.binder_coeff.setDecimals(3)
        self.binder_coeff.setValue(0.44)
        binder_coeff_layout.addWidget(self.binder_coeff)
        
        binder_coeff_help = QToolButton()
        binder_coeff_help.setText("؟")
        binder_coeff_help.setToolTip("معامل الطبقة الرابطة (a2)")
        binder_coeff_help.clicked.connect(lambda: self.show_help("BinderCoeff"))
        binder_coeff_layout.addWidget(binder_coeff_help)
        
        layout.addRow("معامل الطبقة الرابطة (a2):", binder_coeff_layout)
        
        # Base course material
        self.base_material = QComboBox()
        self.load_materials('base')
        self.base_material.currentTextChanged.connect(self.on_base_material_changed)
        layout.addRow("مادة طبقة الأساس:", self.base_material)
        
        # Base coefficient
        base_coeff_layout = QHBoxLayout()
        self.base_coeff = QDoubleSpinBox()
        self.base_coeff.setRange(0.05, 0.4)
        self.base_coeff.setDecimals(3)
        self.base_coeff.setValue(0.14)
        base_coeff_layout.addWidget(self.base_coeff)
        
        base_coeff_help = QToolButton()
        base_coeff_help.setText("؟")
        base_coeff_help.setToolTip("معامل طبقة الأساس (a3)")
        base_coeff_help.clicked.connect(lambda: self.show_help("BaseCoeff"))
        base_coeff_layout.addWidget(base_coeff_help)
        
        layout.addRow("معامل طبقة الأساس (a3):", base_coeff_layout)
        
        parent_layout.addWidget(group)
    
    def create_subgrade_group(self, parent_layout):
        """Create subgrade properties group"""
        group = QGroupBox("خصائص التربة التحتية")
        group.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout = QFormLayout(group)
        
        # Subgrade soil type
        self.subgrade_soil = QComboBox()
        self.load_materials('subgrade')
        self.subgrade_soil.currentTextChanged.connect(self.on_subgrade_soil_changed)
        layout.addRow("نوع التربة التحتية:", self.subgrade_soil)
        
        # CBR input
        cbr_layout = QHBoxLayout()
        self.cbr_input = QDoubleSpinBox()
        self.cbr_input.setRange(1, 100)
        self.cbr_input.setDecimals(1)
        self.cbr_input.setValue(6.0)
        self.cbr_input.setSuffix(" %")
        cbr_layout.addWidget(self.cbr_input)
        
        cbr_help = QToolButton()
        cbr_help.setText("؟")
        cbr_help.setToolTip("نسبة تحمل كاليفورنيا للتربة التحتية")
        cbr_help.clicked.connect(lambda: self.show_help("CBR"))
        cbr_layout.addWidget(cbr_help)
        
        layout.addRow("قيمة CBR:", cbr_layout)
        
        parent_layout.addWidget(group)
    
    def create_design_group(self, parent_layout):
        """Create design parameters group"""
        group = QGroupBox("معاملات التصميم")
        group.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout = QFormLayout(group)
        
        # Design period
        period_layout = QHBoxLayout()
        self.design_period = QSpinBox()
        self.design_period.setRange(10, 50)
        self.design_period.setValue(20)
        self.design_period.setSuffix(" سنة")
        period_layout.addWidget(self.design_period)
        
        period_help = QToolButton()
        period_help.setText("؟")
        period_help.setToolTip("فترة التصميم بالسنوات")
        period_help.clicked.connect(lambda: self.show_help("DesignPeriod"))
        period_layout.addWidget(period_help)
        
        layout.addRow("فترة التصميم:", period_layout)
        
        # Educational mode checkbox
        self.educational_mode = QCheckBox("الوضع التعليمي")
        self.educational_mode.setChecked(True)
        self.educational_mode.setToolTip("عرض شروحات تفصيلية للمعاملات")
        layout.addRow("", self.educational_mode)
        
        parent_layout.addWidget(group)
    
    def create_calculate_button(self, parent_layout):
        """Create calculate button"""
        button_layout = QHBoxLayout()
        
        self.calculate_btn = QPushButton("حساب سماكة الطبقات")
        self.calculate_btn.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        self.calculate_btn.setMinimumHeight(50)
        self.calculate_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        self.calculate_btn.clicked.connect(self.calculate_requested.emit)
        
        button_layout.addWidget(self.calculate_btn)
        parent_layout.addLayout(button_layout)
    
    def setup_connections(self):
        """Setup signal connections"""
        # Connect all input widgets to change signal
        inputs = [
            self.w18_input, self.reliability_input, self.std_input, self.psi_input,
            self.surface_coeff, self.binder_coeff, self.base_coeff, self.cbr_input,
            self.design_period
        ]
        
        for input_widget in inputs:
            if hasattr(input_widget, 'valueChanged'):
                input_widget.valueChanged.connect(self.on_input_changed)
        
        # Connect combo boxes
        combos = [
            self.traffic_category, self.surface_material, self.binder_material,
            self.base_material, self.subgrade_soil
        ]
        
        for combo in combos:
            combo.currentTextChanged.connect(self.on_input_changed)
        
        # Connect reliability input to slider
        self.reliability_input.valueChanged.connect(
            lambda v: self.reliability_slider.setValue(int(v * 10))
        )
    
    def load_default_values(self):
        """Load default values from config"""
        self.reliability_input.setValue(self.config.get('default_reliability', 95.0))
        self.psi_input.setValue(self.config.get('default_serviceability_loss', 2.0))
        self.std_input.setValue(self.config.get('default_standard_deviation', 0.45))
        self.design_period.setValue(self.config.get('default_design_period', 20))
    
    def load_traffic_categories(self):
        """Load traffic categories into combo box"""
        self.traffic_category.clear()
        categories = self.material_manager.get_traffic_categories('ar')
        
        for cat_id, cat_name in categories:
            self.traffic_category.addItem(cat_name, cat_id)
    
    def load_materials(self, layer_type: str):
        """Load materials for specific layer type"""
        combo_map = {
            'surface': self.surface_material,
            'binder': self.binder_material,
            'base': self.base_material,
            'subgrade': self.subgrade_soil
        }
        
        if layer_type in combo_map:
            combo = combo_map[layer_type]
            combo.clear()
            
            materials = self.material_manager.get_materials(layer_type, 'ar')
            for mat_id, mat_name in materials:
                combo.addItem(mat_name, mat_id)
    
    def on_input_changed(self):
        """Handle input change"""
        self.change_timer.start(500)  # Delay signal emission
    
    def on_traffic_category_changed(self, category_name: str):
        """Handle traffic category change"""
        # Find category by name and update W18 range
        for cat_id, cat_name in self.material_manager.get_traffic_categories('ar'):
            if cat_name == category_name:
                category = self.material_manager.get_traffic_category(cat_id)
                if category:
                    # Set W18 to middle of range
                    w18_range = category.w18_range
                    middle_w18 = (w18_range[0] + w18_range[1]) / 2
                    self.w18_input.setValue(middle_w18)
                break
    
    def on_surface_material_changed(self, material_name: str):
        """Handle surface material change"""
        self.update_material_coefficient('surface', self.surface_coeff)
    
    def on_binder_material_changed(self, material_name: str):
        """Handle binder material change"""
        self.update_material_coefficient('binder', self.binder_coeff)
    
    def on_base_material_changed(self, material_name: str):
        """Handle base material change"""
        self.update_material_coefficient('base', self.base_coeff)
    
    def on_subgrade_soil_changed(self, soil_name: str):
        """Handle subgrade soil change"""
        # Find soil by name and update CBR
        for soil_id, soil_name_check in self.material_manager.get_materials('subgrade', 'ar'):
            if soil_name_check == soil_name:
                typical_cbr = self.material_manager.get_typical_cbr(soil_id)
                if typical_cbr:
                    self.cbr_input.setValue(typical_cbr)
                break
    
    def update_material_coefficient(self, layer_type: str, coeff_widget):
        """Update material coefficient based on selection"""
        combo_map = {
            'surface': self.surface_material,
            'binder': self.binder_material,
            'base': self.base_material
        }
        
        if layer_type in combo_map:
            combo = combo_map[layer_type]
            material_id = combo.currentData()
            
            if material_id:
                coefficient = self.material_manager.get_material_coefficient(layer_type, material_id)
                coeff_widget.setValue(coefficient)
    
    def show_help(self, parameter: str):
        """Show help for specific parameter"""
        help_texts = {
            "W18": "عدد المحاور المكافئة 18-kip:\nعدد مرات مرور الأحمال المكافئة لمحور 18 ألف باوند خلال فترة التصميم",
            "Reliability": "الاعتمادية:\nاحتمالية أن يؤدي الطريق وظيفته بشكل مرضي خلال فترة التصميم",
            "StandardDeviation": "الانحراف المعياري:\nمقياس لعدم اليقين في التنبؤ بأداء الطريق",
            "Serviceability": "فقدان الخدمة:\nالفرق بين مؤشر الخدمة الأولي (عادة 4.2) والنهائي (عادة 2.5)",
            "CBR": "نسبة تحمل كاليفورنيا:\nمقياس لقوة تحمل التربة التحتية",
            "SurfaceCoeff": "معامل الطبقة السطحية:\nيعكس قدرة المادة على توزيع الأحمال",
            "BinderCoeff": "معامل الطبقة الرابطة:\nيعكس قدرة المادة على توزيع الأحمال",
            "BaseCoeff": "معامل طبقة الأساس:\nيعكس قدرة المادة على توزيع الأحمال",
            "DesignPeriod": "فترة التصميم:\nالفترة الزمنية المتوقعة لخدمة الطريق"
        }
        
        if parameter in help_texts:
            QMessageBox.information(self, f"مساعدة - {parameter}", help_texts[parameter])
    
    def get_inputs(self) -> DesignInputs:
        """Get current input values as DesignInputs object"""
        return DesignInputs(
            w18=self.w18_input.value(),
            reliability=self.reliability_input.value(),
            serviceability_loss=self.psi_input.value(),
            standard_deviation=self.std_input.value(),
            design_period=self.design_period.value(),
            surface_modulus=450000,  # Default value
            binder_modulus=400000,   # Default value
            base_modulus=30000,      # Default value
            subgrade_cbr=self.cbr_input.value(),
            surface_coefficient=self.surface_coeff.value(),
            binder_coefficient=self.binder_coeff.value(),
            base_coefficient=self.base_coeff.value()
        )
    
    def load_project(self, project):
        """Load project data into input fields"""
        if not project:
            return
        
        self.current_project = project
        inputs = project.inputs
        
        # Load values
        self.w18_input.setValue(inputs.w18)
        self.reliability_input.setValue(inputs.reliability)
        self.psi_input.setValue(inputs.serviceability_loss)
        self.std_input.setValue(inputs.standard_deviation)
        self.design_period.setValue(inputs.design_period)
        self.cbr_input.setValue(inputs.subgrade_cbr)
        
        if inputs.surface_coefficient:
            self.surface_coeff.setValue(inputs.surface_coefficient)
        if inputs.binder_coefficient:
            self.binder_coeff.setValue(inputs.binder_coefficient)
        if inputs.base_coefficient:
            self.base_coeff.setValue(inputs.base_coefficient)
    
    def validate_inputs(self) -> tuple[bool, list[str]]:
        """Validate current inputs"""
        errors = []
        
        if self.w18_input.value() <= 0:
            errors.append("عدد المحاور المكافئة يجب أن يكون أكبر من صفر")
        
        if not (50 <= self.reliability_input.value() <= 99.9):
            errors.append("الاعتمادية يجب أن تكون بين 50% و 99.9%")
        
        if self.psi_input.value() <= 0:
            errors.append("فقدان الخدمة يجب أن يكون أكبر من صفر")
        
        if self.std_input.value() <= 0:
            errors.append("الانحراف المعياري يجب أن يكون أكبر من صفر")
        
        if self.cbr_input.value() <= 0:
            errors.append("قيمة CBR يجب أن تكون أكبر من صفر")
        
        return len(errors) == 0, errors
