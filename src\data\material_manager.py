#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Material Library Manager for Asphalto
Manages material properties and provides recommendations
"""

import json
import os
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

@dataclass
class Material:
    """Material properties"""
    id: str
    name_ar: str
    name_en: str
    coefficient: float
    typical_modulus: Optional[float] = None
    cbr_range: Optional[List[float]] = None
    typical_cbr: Optional[float] = None
    description_ar: str = ""
    description_en: str = ""

@dataclass
class TrafficCategory:
    """Traffic category definition"""
    id: str
    name_ar: str
    name_en: str
    w18_range: List[float]
    description_ar: str = ""
    description_en: str = ""

@dataclass
class ReliabilityRecommendation:
    """Reliability recommendation"""
    id: str
    name_ar: str
    name_en: str
    reliability: float
    description_ar: str = ""
    description_en: str = ""

class MaterialManager:
    """Material library manager"""
    
    def __init__(self, library_path: Optional[str] = None):
        """Initialize material manager"""
        self.library_path = library_path or self._get_default_library_path()
        self.materials = {}
        self.traffic_categories = {}
        self.reliability_recommendations = {}
        self.load_library()
    
    def _get_default_library_path(self) -> str:
        """Get default library path"""
        return os.path.join(
            os.path.dirname(__file__), 
            'material_library.json'
        )
    
    def load_library(self) -> bool:
        """Load material library from JSON file"""
        try:
            if not os.path.exists(self.library_path):
                self._create_default_library()
            
            with open(self.library_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Load surface materials
            self.materials['surface'] = {}
            for mat_id, mat_data in data.get('surface_materials', {}).items():
                self.materials['surface'][mat_id] = Material(
                    id=mat_id,
                    name_ar=mat_data.get('name_ar', ''),
                    name_en=mat_data.get('name_en', ''),
                    coefficient=mat_data.get('coefficient', 0.44),
                    typical_modulus=mat_data.get('typical_modulus'),
                    description_ar=mat_data.get('description_ar', ''),
                    description_en=mat_data.get('description_en', '')
                )
            
            # Load binder materials
            self.materials['binder'] = {}
            for mat_id, mat_data in data.get('binder_materials', {}).items():
                self.materials['binder'][mat_id] = Material(
                    id=mat_id,
                    name_ar=mat_data.get('name_ar', ''),
                    name_en=mat_data.get('name_en', ''),
                    coefficient=mat_data.get('coefficient', 0.44),
                    typical_modulus=mat_data.get('typical_modulus'),
                    description_ar=mat_data.get('description_ar', ''),
                    description_en=mat_data.get('description_en', '')
                )
            
            # Load base materials
            self.materials['base'] = {}
            for mat_id, mat_data in data.get('base_materials', {}).items():
                self.materials['base'][mat_id] = Material(
                    id=mat_id,
                    name_ar=mat_data.get('name_ar', ''),
                    name_en=mat_data.get('name_en', ''),
                    coefficient=mat_data.get('coefficient', 0.14),
                    typical_modulus=mat_data.get('typical_modulus'),
                    cbr_range=mat_data.get('cbr_range'),
                    description_ar=mat_data.get('description_ar', ''),
                    description_en=mat_data.get('description_en', '')
                )
            
            # Load subgrade soils
            self.materials['subgrade'] = {}
            for soil_id, soil_data in data.get('subgrade_soils', {}).items():
                self.materials['subgrade'][soil_id] = Material(
                    id=soil_id,
                    name_ar=soil_data.get('name_ar', ''),
                    name_en=soil_data.get('name_en', ''),
                    coefficient=0.0,  # Not applicable for subgrade
                    cbr_range=soil_data.get('cbr_range'),
                    typical_cbr=soil_data.get('typical_cbr'),
                    description_ar=soil_data.get('description_ar', ''),
                    description_en=soil_data.get('description_en', '')
                )
            
            # Load traffic categories
            for cat_id, cat_data in data.get('traffic_categories', {}).items():
                self.traffic_categories[cat_id] = TrafficCategory(
                    id=cat_id,
                    name_ar=cat_data.get('name_ar', ''),
                    name_en=cat_data.get('name_en', ''),
                    w18_range=cat_data.get('w18_range', [0, 0]),
                    description_ar=cat_data.get('description_ar', ''),
                    description_en=cat_data.get('description_en', '')
                )
            
            # Load reliability recommendations
            for rec_id, rec_data in data.get('reliability_recommendations', {}).items():
                self.reliability_recommendations[rec_id] = ReliabilityRecommendation(
                    id=rec_id,
                    name_ar=rec_data.get('name_ar', ''),
                    name_en=rec_data.get('name_en', ''),
                    reliability=rec_data.get('reliability', 85),
                    description_ar=rec_data.get('description_ar', ''),
                    description_en=rec_data.get('description_en', '')
                )
            
            return True
            
        except Exception as e:
            print(f"Error loading material library: {e}")
            return False
    
    def _create_default_library(self):
        """Create default library file if it doesn't exist"""
        # This would create a minimal default library
        # For now, we'll assume the file exists
        pass
    
    def get_materials(self, layer_type: str, language: str = 'ar') -> List[Tuple[str, str]]:
        """Get list of materials for a layer type"""
        materials = []
        
        if layer_type in self.materials:
            for mat_id, material in self.materials[layer_type].items():
                name = material.name_ar if language == 'ar' else material.name_en
                materials.append((mat_id, name))
        
        return materials
    
    def get_material(self, layer_type: str, material_id: str) -> Optional[Material]:
        """Get specific material"""
        if layer_type in self.materials:
            return self.materials[layer_type].get(material_id)
        return None
    
    def get_material_coefficient(self, layer_type: str, material_id: str) -> float:
        """Get material coefficient (a-value)"""
        material = self.get_material(layer_type, material_id)
        if material:
            return material.coefficient
        
        # Default values
        defaults = {'surface': 0.44, 'binder': 0.44, 'base': 0.14}
        return defaults.get(layer_type, 0.14)
    
    def get_material_modulus(self, layer_type: str, material_id: str) -> Optional[float]:
        """Get typical material modulus"""
        material = self.get_material(layer_type, material_id)
        if material:
            return material.typical_modulus
        return None
    
    def get_subgrade_cbr_range(self, soil_id: str) -> Optional[List[float]]:
        """Get CBR range for subgrade soil"""
        material = self.get_material('subgrade', soil_id)
        if material:
            return material.cbr_range
        return None
    
    def get_typical_cbr(self, soil_id: str) -> Optional[float]:
        """Get typical CBR for subgrade soil"""
        material = self.get_material('subgrade', soil_id)
        if material:
            return material.typical_cbr
        return None
    
    def recommend_subgrade_soil(self, cbr: float) -> Optional[str]:
        """Recommend subgrade soil type based on CBR"""
        for soil_id, material in self.materials.get('subgrade', {}).items():
            if material.cbr_range:
                if material.cbr_range[0] <= cbr <= material.cbr_range[1]:
                    return soil_id
        return None
    
    def get_traffic_categories(self, language: str = 'ar') -> List[Tuple[str, str]]:
        """Get list of traffic categories"""
        categories = []
        
        for cat_id, category in self.traffic_categories.items():
            name = category.name_ar if language == 'ar' else category.name_en
            categories.append((cat_id, name))
        
        return categories
    
    def get_traffic_category(self, category_id: str) -> Optional[TrafficCategory]:
        """Get specific traffic category"""
        return self.traffic_categories.get(category_id)
    
    def recommend_traffic_category(self, w18: float) -> Optional[str]:
        """Recommend traffic category based on W18"""
        for cat_id, category in self.traffic_categories.items():
            if category.w18_range[0] <= w18 <= category.w18_range[1]:
                return cat_id
        return None
    
    def get_reliability_recommendations(self, language: str = 'ar') -> List[Tuple[str, str, float]]:
        """Get list of reliability recommendations"""
        recommendations = []
        
        for rec_id, recommendation in self.reliability_recommendations.items():
            name = recommendation.name_ar if language == 'ar' else recommendation.name_en
            recommendations.append((rec_id, name, recommendation.reliability))
        
        return recommendations
    
    def get_reliability_recommendation(self, rec_id: str) -> Optional[ReliabilityRecommendation]:
        """Get specific reliability recommendation"""
        return self.reliability_recommendations.get(rec_id)
    
    def validate_material_combination(self, surface_id: str, binder_id: str, 
                                    base_id: str) -> List[str]:
        """Validate material combination and return warnings"""
        warnings = []
        
        surface_mat = self.get_material('surface', surface_id)
        binder_mat = self.get_material('binder', binder_id)
        base_mat = self.get_material('base', base_id)
        
        # Check if surface coefficient is higher than binder
        if surface_mat and binder_mat:
            if surface_mat.coefficient < binder_mat.coefficient:
                warnings.append("معامل الطبقة السطحية أقل من الطبقة الرابطة")
        
        # Check if binder coefficient is higher than base
        if binder_mat and base_mat:
            if binder_mat.coefficient < base_mat.coefficient:
                warnings.append("معامل الطبقة الرابطة أقل من طبقة الأساس")
        
        return warnings
    
    def save_library(self) -> bool:
        """Save current library to file"""
        try:
            data = {
                'surface_materials': {},
                'binder_materials': {},
                'base_materials': {},
                'subgrade_soils': {},
                'traffic_categories': {},
                'reliability_recommendations': {}
            }
            
            # Convert materials back to dict format
            for layer_type in ['surface', 'binder', 'base', 'subgrade']:
                if layer_type in self.materials:
                    section_name = f"{layer_type}_materials" if layer_type != 'subgrade' else 'subgrade_soils'
                    for mat_id, material in self.materials[layer_type].items():
                        mat_dict = {
                            'name_ar': material.name_ar,
                            'name_en': material.name_en,
                            'coefficient': material.coefficient,
                            'description_ar': material.description_ar,
                            'description_en': material.description_en
                        }
                        
                        if material.typical_modulus is not None:
                            mat_dict['typical_modulus'] = material.typical_modulus
                        
                        if material.cbr_range is not None:
                            mat_dict['cbr_range'] = material.cbr_range
                        
                        if material.typical_cbr is not None:
                            mat_dict['typical_cbr'] = material.typical_cbr
                        
                        data[section_name][mat_id] = mat_dict
            
            # Convert traffic categories
            for cat_id, category in self.traffic_categories.items():
                data['traffic_categories'][cat_id] = {
                    'name_ar': category.name_ar,
                    'name_en': category.name_en,
                    'w18_range': category.w18_range,
                    'description_ar': category.description_ar,
                    'description_en': category.description_en
                }
            
            # Convert reliability recommendations
            for rec_id, recommendation in self.reliability_recommendations.items():
                data['reliability_recommendations'][rec_id] = {
                    'name_ar': recommendation.name_ar,
                    'name_en': recommendation.name_en,
                    'reliability': recommendation.reliability,
                    'description_ar': recommendation.description_ar,
                    'description_en': recommendation.description_en
                }
            
            with open(self.library_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"Error saving material library: {e}")
            return False
    
    def add_custom_material(self, layer_type: str, material_id: str, 
                          material: Material) -> bool:
        """Add custom material to library"""
        try:
            if layer_type not in self.materials:
                self.materials[layer_type] = {}
            
            self.materials[layer_type][material_id] = material
            return self.save_library()
            
        except Exception as e:
            print(f"Error adding custom material: {e}")
            return False
    
    def remove_material(self, layer_type: str, material_id: str) -> bool:
        """Remove material from library"""
        try:
            if layer_type in self.materials and material_id in self.materials[layer_type]:
                del self.materials[layer_type][material_id]
                return self.save_library()
            return False
            
        except Exception as e:
            print(f"Error removing material: {e}")
            return False
