#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main Window for Asphalto Application
"""

import os
import sys
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QTabWidget, QMenuBar, QStatusBar, QToolBar, 
                            QAction, QMessageBox, QFileDialog, QSplitter,
                            QLabel, QFrame)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QIcon, QPixmap, QFont, QAction

from src.core.config import Config
from src.core.aashto_calculator import AASHTOCalculator
from src.data.material_manager import MaterialManager
from src.data.project_manager import ProjectManager
from src.ui.input_panel import InputPanel
from src.ui.results_panel import ResultsPanel
from src.ui.analysis_panel import AnalysisPanel
from src.ui.settings_dialog import SettingsDialog
from src.ui.about_dialog import AboutDialog

class MainWindow(QMainWindow):
    """Main application window"""
    
    # Signals
    language_changed = pyqtSignal(str)
    theme_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        
        # Initialize core components
        self.config = Config()
        self.calculator = AASHTOCalculator()
        self.material_manager = MaterialManager(self.config.get_material_library_path())
        self.project_manager = ProjectManager(self.config.get_projects_dir())
        
        # Current project
        self.current_project = None
        
        # Auto-save timer
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self._auto_save)
        self.auto_save_timer.start(30000)  # Auto-save every 30 seconds
        
        # Initialize UI
        self.init_ui()
        self.setup_connections()
        self.load_settings()
        
        # Apply theme
        self.apply_theme(self.config.get_theme())
    
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("Asphalto - Professional Asphalt Pavement Design")
        self.setMinimumSize(1200, 800)
        
        # Set window icon
        self.set_window_icon()
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Create input panel
        self.input_panel = InputPanel(self.material_manager, self.config)
        splitter.addWidget(self.input_panel)
        
        # Create right side with tabs
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        right_layout.addWidget(self.tab_widget)
        
        # Create results panel
        self.results_panel = ResultsPanel(self.config)
        self.tab_widget.addTab(self.results_panel, "النتائج")
        
        # Create analysis panel
        self.analysis_panel = AnalysisPanel(self.config)
        self.tab_widget.addTab(self.analysis_panel, "التحليل")
        
        # Add developer signature
        self.add_developer_signature(right_layout)
        
        splitter.addWidget(right_widget)
        
        # Set splitter proportions
        splitter.setSizes([400, 800])
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create status bar
        self.create_status_bar()
    
    def set_window_icon(self):
        """Set application window icon"""
        icon_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
            'assets', 'icons', 'app_icon.png'
        )
        
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
    
    def add_developer_signature(self, layout):
        """Add developer signature to the bottom"""
        signature_frame = QFrame()
        signature_frame.setFrameStyle(QFrame.Shape.Box)
        signature_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        
        signature_layout = QVBoxLayout(signature_frame)
        signature_layout.setContentsMargins(10, 5, 10, 5)
        
        # Title
        title_label = QLabel("🔒 تم تصميم وتطوير فكرة البرنامج بواسطة:")
        title_label.setFont(QFont("Arial", 9, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        signature_layout.addWidget(title_label)
        
        # Developer name
        name_label = QLabel("المهندس محمد يونس الجوالي")
        name_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        name_label.setStyleSheet("color: #2c3e50;")
        signature_layout.addWidget(name_label)
        
        # Contact info
        contact_layout = QHBoxLayout()
        
        whatsapp_label = QLabel("📞 WhatsApp: +964 750 781 2241")
        whatsapp_label.setFont(QFont("Arial", 8))
        contact_layout.addWidget(whatsapp_label)
        
        facebook_label = QLabel("🌐 Facebook: facebook.com/aljwalei")
        facebook_label.setFont(QFont("Arial", 8))
        contact_layout.addWidget(facebook_label)
        
        instagram_label = QLabel("📸 Instagram: instagram.com/m2n.9")
        instagram_label.setFont(QFont("Arial", 8))
        contact_layout.addWidget(instagram_label)
        
        signature_layout.addLayout(contact_layout)
        
        # Copyright
        copyright_label = QLabel("جميع الحقوق محفوظة ©")
        copyright_label.setFont(QFont("Arial", 8))
        copyright_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        signature_layout.addWidget(copyright_label)
        
        layout.addWidget(signature_frame)
    
    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('ملف')
        
        new_action = QAction('مشروع جديد', self)
        new_action.setShortcut('Ctrl+N')
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)
        
        open_action = QAction('فتح مشروع', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)
        
        save_action = QAction('حفظ', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_project)
        file_menu.addAction(save_action)
        
        save_as_action = QAction('حفظ باسم', self)
        save_as_action.setShortcut('Ctrl+Shift+S')
        save_as_action.triggered.connect(self.save_project_as)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        export_action = QAction('تصدير التقرير', self)
        export_action.triggered.connect(self.export_report)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('خروج', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Calculate menu
        calc_menu = menubar.addMenu('حساب')
        
        calculate_action = QAction('حساب السماكة', self)
        calculate_action.setShortcut('F5')
        calculate_action.triggered.connect(self.calculate_thickness)
        calc_menu.addAction(calculate_action)
        
        clear_action = QAction('مسح النتائج', self)
        clear_action.triggered.connect(self.clear_results)
        calc_menu.addAction(clear_action)
        
        # Tools menu
        tools_menu = menubar.addMenu('أدوات')
        
        settings_action = QAction('الإعدادات', self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        material_lib_action = QAction('مكتبة المواد', self)
        material_lib_action.triggered.connect(self.show_material_library)
        tools_menu.addAction(material_lib_action)
        
        # View menu
        view_menu = menubar.addMenu('عرض')
        
        theme_light_action = QAction('المظهر الفاتح', self)
        theme_light_action.triggered.connect(lambda: self.apply_theme('light'))
        view_menu.addAction(theme_light_action)
        
        theme_dark_action = QAction('المظهر الداكن', self)
        theme_dark_action.triggered.connect(lambda: self.apply_theme('dark'))
        view_menu.addAction(theme_dark_action)
        
        view_menu.addSeparator()
        
        lang_ar_action = QAction('العربية', self)
        lang_ar_action.triggered.connect(lambda: self.change_language('ar'))
        view_menu.addAction(lang_ar_action)
        
        lang_en_action = QAction('English', self)
        lang_en_action.triggered.connect(lambda: self.change_language('en'))
        view_menu.addAction(lang_en_action)
        
        # Help menu
        help_menu = menubar.addMenu('مساعدة')
        
        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
        help_action = QAction('دليل المستخدم', self)
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)
    
    def create_toolbar(self):
        """Create application toolbar"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # New project
        new_action = QAction('جديد', self)
        new_action.triggered.connect(self.new_project)
        toolbar.addAction(new_action)
        
        # Open project
        open_action = QAction('فتح', self)
        open_action.triggered.connect(self.open_project)
        toolbar.addAction(open_action)
        
        # Save project
        save_action = QAction('حفظ', self)
        save_action.triggered.connect(self.save_project)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # Calculate
        calc_action = QAction('حساب', self)
        calc_action.triggered.connect(self.calculate_thickness)
        toolbar.addAction(calc_action)
        
        toolbar.addSeparator()
        
        # Settings
        settings_action = QAction('إعدادات', self)
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)
    
    def create_status_bar(self):
        """Create application status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Add permanent widgets
        self.status_label = QLabel("جاهز")
        self.status_bar.addWidget(self.status_label)
        
        # Project status
        self.project_status = QLabel("لا يوجد مشروع")
        self.status_bar.addPermanentWidget(self.project_status)
    
    def setup_connections(self):
        """Setup signal connections"""
        # Connect input panel signals
        self.input_panel.calculate_requested.connect(self.calculate_thickness)
        self.input_panel.inputs_changed.connect(self._on_inputs_changed)
        
        # Connect results panel signals
        self.results_panel.export_requested.connect(self.export_report)
        
        # Connect analysis panel signals
        self.analysis_panel.analysis_requested.connect(self._perform_analysis)
    
    def load_settings(self):
        """Load application settings"""
        # Restore window geometry
        geometry = self.config.get('window_geometry')
        if geometry:
            self.restoreGeometry(geometry)
        
        # Restore window state
        state = self.config.get('window_state')
        if state:
            self.restoreState(state)
    
    def save_settings(self):
        """Save application settings"""
        # Save window geometry and state
        self.config.set('window_geometry', self.saveGeometry())
        self.config.set('window_state', self.saveState())
    
    def apply_theme(self, theme: str):
        """Apply application theme"""
        if theme == 'dark':
            self.setStyleSheet(self._get_dark_theme_stylesheet())
        else:
            self.setStyleSheet(self._get_light_theme_stylesheet())
        
        self.config.set_theme(theme)
        self.theme_changed.emit(theme)
    
    def change_language(self, language: str):
        """Change application language"""
        self.config.set_language(language)
        self.language_changed.emit(language)
        
        # Show restart message
        QMessageBox.information(
            self, 
            "تغيير اللغة", 
            "سيتم تطبيق تغيير اللغة عند إعادة تشغيل البرنامج"
        )
    
    def new_project(self):
        """Create new project"""
        # Check if current project needs saving
        if self.current_project and self._project_has_changes():
            reply = QMessageBox.question(
                self, 
                "مشروع جديد", 
                "هل تريد حفظ المشروع الحالي؟",
                QMessageBox.StandardButton.Yes | 
                QMessageBox.StandardButton.No | 
                QMessageBox.StandardButton.Cancel
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                if not self.save_project():
                    return
            elif reply == QMessageBox.StandardButton.Cancel:
                return
        
        # Create new project
        self.current_project = self.project_manager.create_new_project(
            "مشروع جديد", 
            "وصف المشروع"
        )
        
        # Update UI
        self.input_panel.load_project(self.current_project)
        self.results_panel.clear_results()
        self.analysis_panel.clear_analysis()
        
        self.project_status.setText("مشروع جديد")
        self.status_label.setText("تم إنشاء مشروع جديد")
    
    def open_project(self):
        """Open existing project"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "فتح مشروع",
            self.config.get_projects_dir(),
            "Asphalto Projects (*.asphalto);;All Files (*)"
        )
        
        if file_path:
            project = self.project_manager.load_project(file_path)
            if project:
                self.current_project = project
                self.input_panel.load_project(project)
                
                # Recalculate if needed
                if project.results:
                    self.results_panel.display_results(project.results)
                
                self.project_status.setText(f"مشروع: {project.info.name}")
                self.status_label.setText("تم فتح المشروع بنجاح")
                
                # Add to recent projects
                self.config.add_recent_project(file_path)
            else:
                QMessageBox.warning(self, "خطأ", "فشل في فتح المشروع")
    
    def save_project(self) -> bool:
        """Save current project"""
        if not self.current_project:
            return False
        
        # Update project with current inputs
        self.current_project.inputs = self.input_panel.get_inputs()
        
        # Save project
        if self.project_manager.save_project(self.current_project):
            self.status_label.setText("تم حفظ المشروع")
            return True
        else:
            QMessageBox.warning(self, "خطأ", "فشل في حفظ المشروع")
            return False
    
    def save_project_as(self):
        """Save project with new name"""
        if not self.current_project:
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "حفظ المشروع باسم",
            self.config.get_projects_dir(),
            "Asphalto Projects (*.asphalto);;All Files (*)"
        )
        
        if file_path:
            # Update project with current inputs
            self.current_project.inputs = self.input_panel.get_inputs()
            
            if self.project_manager.save_project(self.current_project, file_path):
                self.status_label.setText("تم حفظ المشروع")
                self.config.add_recent_project(file_path)
            else:
                QMessageBox.warning(self, "خطأ", "فشل في حفظ المشروع")
    
    def calculate_thickness(self):
        """Calculate pavement thickness"""
        try:
            # Get inputs from input panel
            inputs = self.input_panel.get_inputs()
            
            # Perform calculation
            results = self.calculator.calculate(inputs)
            
            # Update current project
            if self.current_project:
                self.current_project.inputs = inputs
                self.current_project.results = results
            
            # Display results
            self.results_panel.display_results(results)
            
            # Update status
            if results.is_valid:
                self.status_label.setText("تم حساب السماكة بنجاح")
            else:
                self.status_label.setText("تحذير: توجد أخطاء في الحساب")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحساب", f"حدث خطأ أثناء الحساب:\n{str(e)}")
            self.status_label.setText("فشل في الحساب")
    
    def clear_results(self):
        """Clear calculation results"""
        self.results_panel.clear_results()
        self.analysis_panel.clear_analysis()
        
        if self.current_project:
            self.current_project.results = None
        
        self.status_label.setText("تم مسح النتائج")
    
    def export_report(self):
        """Export calculation report"""
        if not self.current_project or not self.current_project.results:
            QMessageBox.warning(self, "تحذير", "لا توجد نتائج للتصدير")
            return

        file_path, file_filter = QFileDialog.getSaveFileName(
            self,
            "تصدير التقرير",
            f"{self.current_project.info.name}_تقرير.pdf",
            "PDF Files (*.pdf);;Excel Files (*.xlsx);;All Files (*)"
        )

        if file_path:
            try:
                if file_filter.startswith("PDF"):
                    # Export PDF report
                    from src.utils.report_generator import ReportGenerator
                    generator = ReportGenerator()

                    language = self.config.get_language()
                    include_charts = self.config.get('include_charts', True)

                    success = generator.generate_report(
                        self.current_project,
                        file_path,
                        language=language,
                        include_charts=include_charts,
                        include_analysis=True
                    )

                    if success:
                        self.status_label.setText("تم تصدير التقرير بنجاح")
                        QMessageBox.information(self, "نجح التصدير", f"تم حفظ التقرير في:\n{file_path}")
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في تصدير التقرير")

                elif file_filter.startswith("Excel"):
                    # Export Excel report
                    success = self.project_manager.export_project(
                        self.current_project,
                        file_path,
                        format='excel'
                    )

                    if success:
                        self.status_label.setText("تم تصدير ملف Excel بنجاح")
                        QMessageBox.information(self, "نجح التصدير", f"تم حفظ الملف في:\n{file_path}")
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في تصدير ملف Excel")

            except Exception as e:
                QMessageBox.critical(self, "خطأ في التصدير", f"حدث خطأ أثناء التصدير:\n{str(e)}")
                self.status_label.setText("فشل في التصدير")
    
    def show_settings(self):
        """Show settings dialog"""
        dialog = SettingsDialog(self.config, self)
        if dialog.exec() == SettingsDialog.DialogCode.Accepted:
            # Apply new settings
            self.apply_theme(self.config.get_theme())
    
    def show_material_library(self):
        """Show material library dialog"""
        # TODO: Implement material library dialog
        QMessageBox.information(self, "مكتبة المواد", "سيتم تطوير مكتبة المواد قريباً")
    
    def show_about(self):
        """Show about dialog"""
        dialog = AboutDialog(self)
        dialog.exec()
    
    def show_help(self):
        """Show help documentation"""
        QMessageBox.information(self, "المساعدة", "سيتم إضافة دليل المستخدم قريباً")
    
    def _auto_save(self):
        """Auto-save current project"""
        if self.current_project and self.config.get('auto_save', True):
            self.current_project.inputs = self.input_panel.get_inputs()
            self.project_manager.auto_save_project(self.current_project)
    
    def _on_inputs_changed(self):
        """Handle input changes"""
        # Clear results when inputs change
        self.results_panel.clear_results()
        self.analysis_panel.clear_analysis()
    
    def _perform_analysis(self, analysis_type: str):
        """Perform requested analysis"""
        if not self.current_project or not self.current_project.results:
            QMessageBox.warning(self, "تحذير", "يجب حساب السماكة أولاً")
            return
        
        # TODO: Implement different analysis types
        self.status_label.setText(f"جاري تنفيذ {analysis_type}")
    
    def _project_has_changes(self) -> bool:
        """Check if current project has unsaved changes"""
        if not self.current_project:
            return False
        
        current_inputs = self.input_panel.get_inputs()
        return current_inputs != self.current_project.inputs
    
    def _get_light_theme_stylesheet(self) -> str:
        """Get light theme stylesheet"""
        return """
        QMainWindow {
            background-color: #ffffff;
            color: #333333;
        }
        QTabWidget::pane {
            border: 1px solid #c0c0c0;
            background-color: #ffffff;
        }
        QTabBar::tab {
            background-color: #f0f0f0;
            padding: 8px 16px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: #ffffff;
            border-bottom: 2px solid #3498db;
        }
        """
    
    def _get_dark_theme_stylesheet(self) -> str:
        """Get dark theme stylesheet"""
        return """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #2b2b2b;
        }
        QTabBar::tab {
            background-color: #404040;
            padding: 8px 16px;
            margin-right: 2px;
            color: #ffffff;
        }
        QTabBar::tab:selected {
            background-color: #2b2b2b;
            border-bottom: 2px solid #3498db;
        }
        """
    
    def closeEvent(self, event):
        """Handle application close event"""
        # Check for unsaved changes
        if self.current_project and self._project_has_changes():
            reply = QMessageBox.question(
                self,
                "إغلاق البرنامج",
                "هل تريد حفظ المشروع قبل الإغلاق؟",
                QMessageBox.StandardButton.Yes | 
                QMessageBox.StandardButton.No | 
                QMessageBox.StandardButton.Cancel
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                if not self.save_project():
                    event.ignore()
                    return
            elif reply == QMessageBox.StandardButton.Cancel:
                event.ignore()
                return
        
        # Save settings
        self.save_settings()
        
        # Accept close event
        event.accept()
