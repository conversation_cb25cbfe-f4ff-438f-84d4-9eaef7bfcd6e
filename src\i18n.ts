import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// English translations
const enTranslations = {
  app: {
    title: 'Asphalto - Pavement Thickness Calculator',
    darkMode: 'Dark Mode',
  },
  inputs: {
    w18: 'Equivalent Single Axle Load (W18)',
    psi: 'Serviceability Index (ΔPSI)',
    zr: 'Reliability Factor (ZR)',
    s0: 'Standard Deviation (S0)',
    cbr: 'California Bearing Ratio (CBR)',
    eValues: 'E-Values',
    designPeriod: 'Design Period (Years)',
  },
  // Add more translations as needed
};

// Arabic translations
const arTranslations = {
  app: {
    title: 'أسفلتو - حاسبة سماكة الرصف',
    darkMode: 'الوضع الداكن',
  },
  inputs: {
    w18: 'عدد المحاور المكافئة (W18)',
    psi: 'معامل الخدمة (ΔPSI)',
    zr: 'معامل الاعتمادية (ZR)',
    s0: 'الانحراف القياسي (S0)',
    cbr: 'نسبة تحمل كاليفورنيا (CBR)',
    eValues: 'قيم E',
    designPeriod: 'فترة التصميم (سنوات)',
  },
  // Add more translations as needed
};

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: enTranslations },
      ar: { translation: arTranslations },
    },
    lng: 'ar', // Default language
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
