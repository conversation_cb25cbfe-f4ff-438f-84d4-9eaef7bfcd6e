#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Asphalto - Professional Asphalt Pavement Thickness Calculator
Based on AASHTO 1993 Design Method

Developed by: Engineer <PERSON>
WhatsApp: https://wa.me/9647507812241
Facebook: facebook.com/aljwalei
Instagram: instagram.com/m2n.9
All Rights Reserved ©
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt, QTranslator, QLocale
from PyQt6.QtGui import QIcon, QFont

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.main_window import MainWindow
from src.core.config import Config

def main():
    """Main application entry point"""
    # Create QApplication instance
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Asphalto")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("<PERSON>")
    app.setOrganizationDomain("aljwalei.com")
    
    # Set application icon
    icon_path = os.path.join(os.path.dirname(__file__), 'assets', 'icons', 'app_icon.png')
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
    
    # Set default font for better Arabic support
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    # Enable high DPI scaling
    app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    
    # Load configuration
    config = Config()
    
    # Setup translator for internationalization
    translator = QTranslator()
    locale = config.get_language()
    
    if locale == 'ar':
        translator_file = os.path.join(os.path.dirname(__file__), 'translations', 'asphalto_ar.qm')
        if os.path.exists(translator_file):
            translator.load(translator_file)
            app.installTranslator(translator)
    
    # Create and show main window
    main_window = MainWindow()
    main_window.show()
    
    # Start the application event loop
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
