#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Report Generator for Asphalto Application
Generates professional PDF reports with charts and analysis
"""

import os
import io
from datetime import datetime
from typing import Optional, List, Dict, Any

from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.lib.colors import Color, black, blue, red, green
from reportlab.platypus import (SimpleDocTemplate, Paragraph, Spacer, Table, 
                               TableStyle, PageBreak, Image, Frame, PageTemplate)
from reportlab.platypus.tableofcontents import TableOfContents
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT, TA_JUSTIFY
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend

from src.core.aashto_calculator import DesignResults
from src.data.project_manager import Project

class ReportGenerator:
    """Professional PDF report generator"""
    
    def __init__(self):
        """Initialize report generator"""
        self.setup_fonts()
        self.setup_styles()
    
    def setup_fonts(self):
        """Setup Arabic fonts for PDF"""
        try:
            # Try to register Arabic fonts
            # Note: In production, you would include actual font files
            # For now, we'll use default fonts with Arabic support
            pass
        except Exception as e:
            print(f"Warning: Could not setup Arabic fonts: {e}")
    
    def setup_styles(self):
        """Setup paragraph styles"""
        self.styles = getSampleStyleSheet()
        
        # Arabic title style
        self.styles.add(ParagraphStyle(
            name='ArabicTitle',
            parent=self.styles['Title'],
            fontSize=18,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=Color(0.17, 0.24, 0.31)  # Dark blue
        ))
        
        # Arabic heading style
        self.styles.add(ParagraphStyle(
            name='ArabicHeading',
            parent=self.styles['Heading1'],
            fontSize=14,
            spaceAfter=12,
            alignment=TA_RIGHT,
            textColor=Color(0.17, 0.24, 0.31)
        ))
        
        # Arabic normal style
        self.styles.add(ParagraphStyle(
            name='ArabicNormal',
            parent=self.styles['Normal'],
            fontSize=11,
            alignment=TA_RIGHT,
            spaceAfter=6
        ))
        
        # English styles
        self.styles.add(ParagraphStyle(
            name='EnglishTitle',
            parent=self.styles['Title'],
            fontSize=18,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=Color(0.17, 0.24, 0.31)
        ))
        
        self.styles.add(ParagraphStyle(
            name='EnglishHeading',
            parent=self.styles['Heading1'],
            fontSize=14,
            spaceAfter=12,
            alignment=TA_LEFT,
            textColor=Color(0.17, 0.24, 0.31)
        ))
    
    def generate_report(self, project: Project, output_path: str, 
                       language: str = 'ar', include_charts: bool = True,
                       include_analysis: bool = True) -> bool:
        """Generate complete PDF report"""
        try:
            # Create PDF document
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )
            
            # Build story (content)
            story = []
            
            # Add cover page
            self.add_cover_page(story, project, language)
            story.append(PageBreak())
            
            # Add project information
            self.add_project_info(story, project, language)
            
            # Add design inputs
            self.add_design_inputs(story, project, language)
            
            # Add results
            if project.results:
                self.add_results_section(story, project, language)
                
                # Add charts if requested
                if include_charts:
                    self.add_charts_section(story, project, language)
                
                # Add analysis if requested
                if include_analysis:
                    self.add_analysis_section(story, project, language)
            
            # Add appendices
            self.add_appendices(story, project, language)
            
            # Build PDF
            doc.build(story)
            
            return True
            
        except Exception as e:
            print(f"Error generating report: {e}")
            return False
    
    def add_cover_page(self, story: List, project: Project, language: str):
        """Add cover page to report"""
        if language == 'ar':
            # Arabic cover page
            story.append(Spacer(1, 2*inch))
            
            title = Paragraph("تقرير تصميم الرصف الإسفلتي", self.styles['ArabicTitle'])
            story.append(title)
            story.append(Spacer(1, 0.5*inch))
            
            subtitle = Paragraph("وفقاً لمعايير AASHTO 1993", self.styles['ArabicHeading'])
            story.append(subtitle)
            story.append(Spacer(1, 1*inch))
            
            project_name = Paragraph(f"اسم المشروع: {project.info.name}", self.styles['ArabicNormal'])
            story.append(project_name)
            story.append(Spacer(1, 0.2*inch))
            
            if project.info.location:
                location = Paragraph(f"الموقع: {project.info.location}", self.styles['ArabicNormal'])
                story.append(location)
                story.append(Spacer(1, 0.2*inch))
            
            if project.info.author:
                author = Paragraph(f"المهندس المسؤول: {project.info.author}", self.styles['ArabicNormal'])
                story.append(author)
                story.append(Spacer(1, 0.2*inch))
            
            date = Paragraph(f"تاريخ التقرير: {datetime.now().strftime('%Y/%m/%d')}", self.styles['ArabicNormal'])
            story.append(date)
            
        else:
            # English cover page
            story.append(Spacer(1, 2*inch))
            
            title = Paragraph("Asphalt Pavement Design Report", self.styles['EnglishTitle'])
            story.append(title)
            story.append(Spacer(1, 0.5*inch))
            
            subtitle = Paragraph("Based on AASHTO 1993 Method", self.styles['EnglishHeading'])
            story.append(subtitle)
            story.append(Spacer(1, 1*inch))
            
            project_name = Paragraph(f"Project Name: {project.info.name}", self.styles['Normal'])
            story.append(project_name)
            story.append(Spacer(1, 0.2*inch))
            
            if project.info.location:
                location = Paragraph(f"Location: {project.info.location}", self.styles['Normal'])
                story.append(location)
                story.append(Spacer(1, 0.2*inch))
            
            if project.info.author:
                author = Paragraph(f"Engineer: {project.info.author}", self.styles['Normal'])
                story.append(author)
                story.append(Spacer(1, 0.2*inch))
            
            date = Paragraph(f"Report Date: {datetime.now().strftime('%m/%d/%Y')}", self.styles['Normal'])
            story.append(date)
        
        # Add developer signature
        story.append(Spacer(1, 2*inch))
        
        signature_text = """
        <para align="center">
        <b>تم إنشاء هذا التقرير باستخدام برنامج Asphalto</b><br/>
        <b>تطوير: المهندس محمد يونس الجوالي</b><br/>
        WhatsApp: +964 750 781 2241<br/>
        Facebook: facebook.com/aljwalei<br/>
        جميع الحقوق محفوظة ©
        </para>
        """
        
        signature = Paragraph(signature_text, self.styles['Normal'])
        story.append(signature)
    
    def add_project_info(self, story: List, project: Project, language: str):
        """Add project information section"""
        if language == 'ar':
            heading = Paragraph("معلومات المشروع", self.styles['ArabicHeading'])
        else:
            heading = Paragraph("Project Information", self.styles['EnglishHeading'])
        
        story.append(heading)
        
        # Create project info table
        data = []
        
        if language == 'ar':
            data.extend([
                ['اسم المشروع', project.info.name],
                ['الوصف', project.info.description or 'غير محدد'],
                ['الموقع', project.info.location or 'غير محدد'],
                ['نوع الطريق', project.info.road_type or 'غير محدد'],
                ['المهندس المسؤول', project.info.author or 'غير محدد'],
                ['تاريخ الإنشاء', project.info.created_date[:10] if project.info.created_date else 'غير محدد'],
                ['آخر تعديل', project.info.modified_date[:10] if project.info.modified_date else 'غير محدد'],
            ])
        else:
            data.extend([
                ['Project Name', project.info.name],
                ['Description', project.info.description or 'Not specified'],
                ['Location', project.info.location or 'Not specified'],
                ['Road Type', project.info.road_type or 'Not specified'],
                ['Engineer', project.info.author or 'Not specified'],
                ['Created Date', project.info.created_date[:10] if project.info.created_date else 'Not specified'],
                ['Last Modified', project.info.modified_date[:10] if project.info.modified_date else 'Not specified'],
            ])
        
        table = Table(data, colWidths=[4*cm, 10*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), Color(0.9, 0.9, 0.9)),
            ('TEXTCOLOR', (0, 0), (-1, -1), black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT' if language == 'ar' else 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 0), (-1, 0), Color(0.8, 0.8, 0.8)),
            ('GRID', (0, 0), (-1, -1), 1, black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 0.5*inch))
    
    def add_design_inputs(self, story: List, project: Project, language: str):
        """Add design inputs section"""
        if language == 'ar':
            heading = Paragraph("معاملات التصميم المدخلة", self.styles['ArabicHeading'])
        else:
            heading = Paragraph("Design Input Parameters", self.styles['EnglishHeading'])
        
        story.append(heading)
        
        inputs = project.inputs
        data = []
        
        if language == 'ar':
            data.extend([
                ['عدد المحاور المكافئة (W18)', f"{inputs.w18:,.0f}", 'ESAL'],
                ['مستوى الاعتمادية', f"{inputs.reliability:.1f}", '%'],
                ['فقدان الخدمة (ΔPSI)', f"{inputs.serviceability_loss:.1f}", ''],
                ['الانحراف المعياري (S0)', f"{inputs.standard_deviation:.2f}", ''],
                ['فترة التصميم', f"{inputs.design_period}", 'سنة'],
                ['CBR التربة التحتية', f"{inputs.subgrade_cbr:.1f}", '%'],
                ['معامل الطبقة السطحية (a1)', f"{inputs.surface_coefficient or 0.44:.3f}", ''],
                ['معامل الطبقة الرابطة (a2)', f"{inputs.binder_coefficient or 0.44:.3f}", ''],
                ['معامل طبقة الأساس (a3)', f"{inputs.base_coefficient or 0.14:.3f}", ''],
            ])
        else:
            data.extend([
                ['18-kip ESALs (W18)', f"{inputs.w18:,.0f}", 'ESAL'],
                ['Reliability Level', f"{inputs.reliability:.1f}", '%'],
                ['Serviceability Loss (ΔPSI)', f"{inputs.serviceability_loss:.1f}", ''],
                ['Standard Deviation (S0)', f"{inputs.standard_deviation:.2f}", ''],
                ['Design Period', f"{inputs.design_period}", 'years'],
                ['Subgrade CBR', f"{inputs.subgrade_cbr:.1f}", '%'],
                ['Surface Layer Coefficient (a1)', f"{inputs.surface_coefficient or 0.44:.3f}", ''],
                ['Binder Layer Coefficient (a2)', f"{inputs.binder_coefficient or 0.44:.3f}", ''],
                ['Base Layer Coefficient (a3)', f"{inputs.base_coefficient or 0.14:.3f}", ''],
            ])
        
        table = Table(data, colWidths=[6*cm, 4*cm, 2*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), Color(0.9, 0.9, 0.9)),
            ('TEXTCOLOR', (0, 0), (-1, -1), black),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT' if language == 'ar' else 'LEFT'),
            ('ALIGN', (1, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 0.5*inch))
    
    def add_results_section(self, story: List, project: Project, language: str):
        """Add results section"""
        if not project.results:
            return
        
        results = project.results
        
        if language == 'ar':
            heading = Paragraph("نتائج التصميم", self.styles['ArabicHeading'])
        else:
            heading = Paragraph("Design Results", self.styles['EnglishHeading'])
        
        story.append(heading)
        
        # Results summary
        data = []
        
        if language == 'ar':
            data.extend([
                ['الرقم الهيكلي المطلوب (SN)', f"{results.structural_number:.3f}", ''],
                ['السماكة الكلية', f"{results.layer_thickness.total:.1f}", 'سم'],
                ['سماكة الطبقة السطحية', f"{results.layer_thickness.surface:.1f}", 'سم'],
                ['سماكة الطبقة الرابطة', f"{results.layer_thickness.binder:.1f}", 'سم'],
                ['سماكة طبقة الأساس', f"{results.layer_thickness.base:.1f}", 'سم'],
                ['معامل مرونة التربة التحتية', f"{results.subgrade_modulus:.0f}", 'psi'],
                ['معامل الاعتمادية (ZR)', f"{results.reliability_factor:.3f}", ''],
            ])
        else:
            data.extend([
                ['Required Structural Number (SN)', f"{results.structural_number:.3f}", ''],
                ['Total Thickness', f"{results.layer_thickness.total:.1f}", 'cm'],
                ['Surface Course Thickness', f"{results.layer_thickness.surface:.1f}", 'cm'],
                ['Binder Course Thickness', f"{results.layer_thickness.binder:.1f}", 'cm'],
                ['Base Course Thickness', f"{results.layer_thickness.base:.1f}", 'cm'],
                ['Subgrade Resilient Modulus', f"{results.subgrade_modulus:.0f}", 'psi'],
                ['Reliability Factor (ZR)', f"{results.reliability_factor:.3f}", ''],
            ])
        
        table = Table(data, colWidths=[6*cm, 4*cm, 2*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), Color(0.7, 0.9, 0.7)),  # Light green
            ('TEXTCOLOR', (0, 0), (-1, -1), black),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT' if language == 'ar' else 'LEFT'),
            ('ALIGN', (1, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 0.3*inch))
        
        # Add warnings if any
        if results.warnings:
            if language == 'ar':
                warning_heading = Paragraph("تحذيرات", self.styles['ArabicHeading'])
            else:
                warning_heading = Paragraph("Warnings", self.styles['EnglishHeading'])
            
            story.append(warning_heading)
            
            for warning in results.warnings:
                warning_text = Paragraph(f"• {warning}", self.styles['ArabicNormal' if language == 'ar' else 'Normal'])
                story.append(warning_text)
            
            story.append(Spacer(1, 0.3*inch))
    
    def add_charts_section(self, story: List, project: Project, language: str):
        """Add charts section"""
        if not project.results:
            return
        
        if language == 'ar':
            heading = Paragraph("الرسوم البيانية", self.styles['ArabicHeading'])
        else:
            heading = Paragraph("Charts and Diagrams", self.styles['EnglishHeading'])
        
        story.append(heading)
        
        # Create layer thickness chart
        chart_path = self.create_layer_chart(project.results, language)
        if chart_path and os.path.exists(chart_path):
            img = Image(chart_path, width=6*inch, height=4*inch)
            story.append(img)
            story.append(Spacer(1, 0.3*inch))
            
            # Clean up temporary file
            try:
                os.remove(chart_path)
            except:
                pass
    
    def add_analysis_section(self, story: List, project: Project, language: str):
        """Add analysis section"""
        if language == 'ar':
            heading = Paragraph("التحليل والتوصيات", self.styles['ArabicHeading'])
        else:
            heading = Paragraph("Analysis and Recommendations", self.styles['EnglishHeading'])
        
        story.append(heading)
        
        # Add analysis text based on results
        if project.results:
            analysis_text = self.generate_analysis_text(project.results, language)
            for paragraph in analysis_text:
                story.append(paragraph)
                story.append(Spacer(1, 0.1*inch))
    
    def add_appendices(self, story: List, project: Project, language: str):
        """Add appendices section"""
        story.append(PageBreak())
        
        if language == 'ar':
            heading = Paragraph("الملاحق", self.styles['ArabicHeading'])
        else:
            heading = Paragraph("Appendices", self.styles['EnglishHeading'])
        
        story.append(heading)
        
        # Add AASHTO equation
        if language == 'ar':
            equation_heading = Paragraph("معادلة AASHTO 1993", self.styles['ArabicHeading'])
        else:
            equation_heading = Paragraph("AASHTO 1993 Equation", self.styles['EnglishHeading'])
        
        story.append(equation_heading)
        
        equation_text = """
        log₁₀(W₁₈) = ZR × S₀ + 9.36 × log₁₀(SN + 1) - 0.20 + 
        [log₁₀(ΔPSI/(4.2-1.5))] / [0.40 + 1094/(SN+1)^5.19] + 2.32 × log₁₀(MR) - 8.07
        """
        
        equation = Paragraph(equation_text, self.styles['Normal'])
        story.append(equation)
    
    def create_layer_chart(self, results, language: str) -> Optional[str]:
        """Create layer thickness chart"""
        try:
            fig, ax = plt.subplots(figsize=(8, 6))
            
            # Layer data
            layers = [
                ("Surface" if language == 'en' else "الطبقة السطحية", results.layer_thickness.surface),
                ("Binder" if language == 'en' else "الطبقة الرابطة", results.layer_thickness.binder),
                ("Base" if language == 'en' else "طبقة الأساس", results.layer_thickness.base),
            ]
            
            # Filter out zero thickness layers
            layers = [(name, thickness) for name, thickness in layers if thickness > 0]
            
            names = [layer[0] for layer in layers]
            thicknesses = [layer[1] for layer in layers]
            
            # Create bar chart
            bars = ax.bar(names, thicknesses, color=['#2c3e50', '#34495e', '#7f8c8d'])
            
            # Add value labels on bars
            for bar, thickness in zip(bars, thicknesses):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                       f'{thickness:.1f} cm', ha='center', va='bottom')
            
            ax.set_ylabel('Thickness (cm)' if language == 'en' else 'السماكة (سم)')
            ax.set_title('Layer Thicknesses' if language == 'en' else 'سماكات الطبقات')
            ax.grid(True, alpha=0.3, axis='y')
            
            # Save to temporary file
            temp_path = f"temp_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(temp_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            return temp_path
            
        except Exception as e:
            print(f"Error creating chart: {e}")
            return None
    
    def generate_analysis_text(self, results, language: str) -> List[Paragraph]:
        """Generate analysis text based on results"""
        paragraphs = []
        
        if language == 'ar':
            # Arabic analysis
            analysis_points = [
                f"تم حساب الرقم الهيكلي المطلوب بقيمة {results.structural_number:.3f} وفقاً لمعادلة AASHTO 1993.",
                f"السماكة الكلية المطلوبة للرصف هي {results.layer_thickness.total:.1f} سم.",
            ]
            
            # Add specific recommendations
            if results.layer_thickness.total > 40:
                analysis_points.append("السماكة الكلية مرتفعة، يُنصح بمراجعة معاملات التصميم أو تحسين التربة التحتية.")
            
            if results.layer_thickness.surface < 7.5:
                analysis_points.append("سماكة الطبقة السطحية أقل من الحد الأدنى المُوصى به (7.5 سم).")
            
            if results.inputs.subgrade_cbr < 3:
                analysis_points.append("قيمة CBR للتربة التحتية منخفضة جداً، يُنصح بتحسين التربة.")
            
        else:
            # English analysis
            analysis_points = [
                f"The required structural number was calculated as {results.structural_number:.3f} according to AASHTO 1993 equation.",
                f"The total required pavement thickness is {results.layer_thickness.total:.1f} cm.",
            ]
            
            # Add specific recommendations
            if results.layer_thickness.total > 40:
                analysis_points.append("The total thickness is high, consider reviewing design parameters or improving subgrade.")
            
            if results.layer_thickness.surface < 7.5:
                analysis_points.append("Surface course thickness is below recommended minimum (7.5 cm).")
            
            if results.inputs.subgrade_cbr < 3:
                analysis_points.append("Subgrade CBR is very low, subgrade improvement is recommended.")
        
        # Convert to paragraphs
        for point in analysis_points:
            style = 'ArabicNormal' if language == 'ar' else 'Normal'
            paragraphs.append(Paragraph(f"• {point}", self.styles[style]))
        
        return paragraphs
