#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Basic test script for Asphalto application
Tests core functionality without GUI
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_aashto_calculator():
    """Test AASHTO calculator core functionality"""
    print("Testing AASHTO Calculator...")
    
    try:
        from src.core.aashto_calculator import AASHTOCalculator, DesignInputs
        
        # Create calculator instance
        calculator = AASHTOCalculator()
        print("✅ Calculator created successfully")
        
        # Create test inputs
        inputs = DesignInputs(
            w18=1000000,
            reliability=95.0,
            serviceability_loss=2.0,
            standard_deviation=0.45,
            design_period=20,
            surface_modulus=450000,
            binder_modulus=400000,
            base_modulus=30000,
            subgrade_cbr=6.0
        )
        print("✅ Test inputs created")
        
        # Perform calculation
        results = calculator.calculate(inputs)
        print("✅ Calculation completed")
        
        # Check results
        if results.is_valid:
            print(f"✅ Results valid - SN: {results.structural_number:.3f}")
            print(f"   Total thickness: {results.layer_thickness.total:.1f} cm")
            print(f"   Surface: {results.layer_thickness.surface:.1f} cm")
            print(f"   Binder: {results.layer_thickness.binder:.1f} cm")
            print(f"   Base: {results.layer_thickness.base:.1f} cm")
        else:
            print("❌ Results invalid")
            for error in results.errors:
                print(f"   Error: {error}")
        
        return True
        
    except Exception as e:
        print(f"❌ Calculator test failed: {e}")
        return False

def test_material_manager():
    """Test material manager functionality"""
    print("\nTesting Material Manager...")
    
    try:
        from src.data.material_manager import MaterialManager
        
        # Create material manager
        manager = MaterialManager()
        print("✅ Material manager created")
        
        # Test loading materials
        surface_materials = manager.get_materials('surface', 'ar')
        print(f"✅ Loaded {len(surface_materials)} surface materials")
        
        base_materials = manager.get_materials('base', 'ar')
        print(f"✅ Loaded {len(base_materials)} base materials")
        
        subgrade_soils = manager.get_materials('subgrade', 'ar')
        print(f"✅ Loaded {len(subgrade_soils)} subgrade soils")
        
        # Test coefficient retrieval
        surface_coeff = manager.get_material_coefficient('surface', 'dense_graded_ac')
        print(f"✅ Surface coefficient: {surface_coeff}")
        
        return True
        
    except Exception as e:
        print(f"❌ Material manager test failed: {e}")
        return False

def test_config():
    """Test configuration system"""
    print("\nTesting Configuration...")
    
    try:
        from src.core.config import Config
        
        # Create config instance
        config = Config()
        print("✅ Config created")
        
        # Test getting/setting values
        language = config.get_language()
        print(f"✅ Language: {language}")
        
        theme = config.get_theme()
        print(f"✅ Theme: {theme}")
        
        projects_dir = config.get_projects_dir()
        print(f"✅ Projects directory: {projects_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

def test_project_manager():
    """Test project manager functionality"""
    print("\nTesting Project Manager...")
    
    try:
        from src.data.project_manager import ProjectManager
        from src.core.config import Config
        
        config = Config()
        manager = ProjectManager(config.get_projects_dir())
        print("✅ Project manager created")
        
        # Create test project
        project = manager.create_new_project(
            "Test Project",
            "Test description",
            "Test Author"
        )
        print("✅ Test project created")
        
        # Test project list
        projects = manager.get_project_list()
        print(f"✅ Found {len(projects)} existing projects")
        
        return True
        
    except Exception as e:
        print(f"❌ Project manager test failed: {e}")
        return False

def test_imports():
    """Test all critical imports"""
    print("\nTesting Imports...")
    
    imports_to_test = [
        ('PyQt6.QtWidgets', 'QApplication'),
        ('PyQt6.QtCore', 'Qt'),
        ('PyQt6.QtGui', 'QFont'),
        ('matplotlib.pyplot', None),
        ('numpy', None),
        ('scipy', None),
        ('pandas', None),
    ]
    
    all_passed = True
    
    for module_name, attr_name in imports_to_test:
        try:
            module = __import__(module_name, fromlist=[attr_name] if attr_name else [])
            if attr_name:
                getattr(module, attr_name)
            print(f"✅ {module_name} imported successfully")
        except ImportError as e:
            print(f"❌ Failed to import {module_name}: {e}")
            all_passed = False
        except AttributeError as e:
            print(f"❌ Failed to access {attr_name} in {module_name}: {e}")
            all_passed = False
    
    return all_passed

def main():
    """Run all tests"""
    print("🧪 Running Asphalto Basic Tests")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Material Manager", test_material_manager),
        ("Project Manager", test_project_manager),
        ("AASHTO Calculator", test_aashto_calculator),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application core is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
