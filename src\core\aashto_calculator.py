#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AASHTO 1993 Pavement Design Calculator
Core calculation engine for asphalt pavement thickness design
"""

import math
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import numpy as np

@dataclass
class DesignInputs:
    """Input parameters for AASHTO 1993 design"""
    w18: float  # 18-kip equivalent single axle loads
    reliability: float  # Reliability level (%)
    serviceability_loss: float  # ΔPSI (Initial - Terminal serviceability)
    standard_deviation: float  # Overall standard deviation (S0)
    design_period: int  # Design period in years
    
    # Layer properties
    surface_modulus: float  # Surface course modulus (psi)
    binder_modulus: float  # Binder course modulus (psi)
    base_modulus: float  # Base course modulus (psi)
    subgrade_cbr: float  # Subgrade CBR (%)
    
    # Layer coefficients (a-values)
    surface_coefficient: Optional[float] = None  # a1
    binder_coefficient: Optional[float] = None   # a2
    base_coefficient: Optional[float] = None     # a3
    
    # Drainage coefficients (m-values)
    binder_drainage: float = 1.0  # m2
    base_drainage: float = 1.0    # m3

@dataclass
class LayerThickness:
    """Layer thickness results"""
    surface: float  # Surface course thickness (cm)
    binder: float   # Binder course thickness (cm)
    base: float     # Base course thickness (cm)
    total: float    # Total thickness (cm)

@dataclass
class DesignResults:
    """Complete design results"""
    structural_number: float  # Required SN
    layer_thickness: LayerThickness
    inputs: DesignInputs
    
    # Additional calculations
    subgrade_modulus: float  # Effective roadbed soil resilient modulus (psi)
    reliability_factor: float  # ZR value
    
    # Validation flags
    is_valid: bool = True
    warnings: List[str] = None
    errors: List[str] = None

class AASHTOCalculator:
    """AASHTO 1993 pavement design calculator"""
    
    # Standard reliability factors (ZR values)
    RELIABILITY_FACTORS = {
        50: 0.000,
        60: -0.253,
        70: -0.524,
        75: -0.674,
        80: -0.841,
        85: -1.037,
        90: -1.282,
        95: -1.645,
        99: -2.326,
        99.9: -3.090
    }
    
    # Default layer coefficients
    DEFAULT_COEFFICIENTS = {
        'surface': {
            'dense_graded_ac': 0.44,
            'open_graded_ac': 0.38,
            'sand_asphalt': 0.35
        },
        'binder': {
            'dense_graded_ac': 0.44,
            'open_graded_ac': 0.38,
            'emulsion_mix': 0.30
        },
        'base': {
            'crushed_stone': 0.14,
            'dense_graded_aggregate': 0.12,
            'soil_cement': 0.20,
            'cement_treated': 0.23,
            'asphalt_treated': 0.34
        }
    }
    
    def __init__(self):
        """Initialize calculator"""
        self.last_calculation = None
    
    def calculate_reliability_factor(self, reliability: float) -> float:
        """Calculate ZR factor from reliability percentage"""
        if reliability in self.RELIABILITY_FACTORS:
            return self.RELIABILITY_FACTORS[reliability]
        
        # Interpolate for values not in table
        reliabilities = sorted(self.RELIABILITY_FACTORS.keys())
        
        if reliability < reliabilities[0]:
            return self.RELIABILITY_FACTORS[reliabilities[0]]
        if reliability > reliabilities[-1]:
            return self.RELIABILITY_FACTORS[reliabilities[-1]]
        
        # Linear interpolation
        for i in range(len(reliabilities) - 1):
            r1, r2 = reliabilities[i], reliabilities[i + 1]
            if r1 <= reliability <= r2:
                zr1, zr2 = self.RELIABILITY_FACTORS[r1], self.RELIABILITY_FACTORS[r2]
                return zr1 + (zr2 - zr1) * (reliability - r1) / (r2 - r1)
        
        return 0.0
    
    def calculate_subgrade_modulus(self, cbr: float) -> float:
        """Calculate effective roadbed soil resilient modulus from CBR"""
        if cbr <= 0:
            raise ValueError("CBR must be positive")
        
        # AASHTO correlation: MR = 1500 × CBR (for CBR < 10%)
        # For CBR >= 10%: MR = 3000 × CBR^0.65
        if cbr < 10:
            return 1500 * cbr
        else:
            return 3000 * (cbr ** 0.65)
    
    def calculate_layer_coefficient(self, layer_type: str, material_type: str, 
                                  modulus: Optional[float] = None) -> float:
        """Calculate layer coefficient (a-value) from material properties"""
        
        if layer_type in self.DEFAULT_COEFFICIENTS:
            if material_type in self.DEFAULT_COEFFICIENTS[layer_type]:
                return self.DEFAULT_COEFFICIENTS[layer_type][material_type]
        
        # If modulus is provided, use correlations
        if modulus and layer_type == 'surface':
            # For asphalt concrete: a1 = 0.44 (typical for dense-graded AC)
            return 0.44
        elif modulus and layer_type == 'base':
            # For granular base: a3 = 0.249 × log10(EBS) - 0.977
            # where EBS is base modulus in psi
            if modulus > 0:
                return max(0.05, 0.249 * math.log10(modulus) - 0.977)
        
        # Default values
        defaults = {'surface': 0.44, 'binder': 0.44, 'base': 0.14}
        return defaults.get(layer_type, 0.14)
    
    def calculate_structural_number(self, inputs: DesignInputs) -> float:
        """Calculate required structural number using AASHTO 1993 equation"""
        
        # Validate inputs
        if inputs.w18 <= 0:
            raise ValueError("W18 must be positive")
        if not (0 < inputs.reliability < 100):
            raise ValueError("Reliability must be between 0 and 100")
        if inputs.serviceability_loss <= 0:
            raise ValueError("Serviceability loss must be positive")
        if inputs.standard_deviation <= 0:
            raise ValueError("Standard deviation must be positive")
        
        # Calculate reliability factor
        zr = self.calculate_reliability_factor(inputs.reliability)
        
        # Calculate subgrade modulus
        mr = self.calculate_subgrade_modulus(inputs.subgrade_cbr)
        
        # AASHTO 1993 equation for structural number
        # log10(W18) = ZR×S0 + 9.36×log10(SN+1) - 0.20 + [log10(ΔPSI/(4.2-1.5))]/(0.40+1094/(SN+1)^5.19) + 2.32×log10(MR) - 8.07
        
        # This is an iterative solution since SN appears on both sides
        # We'll use Newton-Raphson method to solve for SN
        
        def aashto_equation(sn):
            """AASHTO equation to solve for SN"""
            if sn <= 0:
                return float('inf')
            
            term1 = zr * inputs.standard_deviation
            term2 = 9.36 * math.log10(sn + 1) - 0.20
            
            psi_ratio = inputs.serviceability_loss / (4.2 - 1.5)
            if psi_ratio <= 0:
                psi_ratio = 0.01
            
            term3_num = math.log10(psi_ratio)
            term3_den = 0.40 + 1094 / ((sn + 1) ** 5.19)
            term3 = term3_num / term3_den
            
            term4 = 2.32 * math.log10(mr) - 8.07
            
            return math.log10(inputs.w18) - (term1 + term2 + term3 + term4)
        
        def aashto_derivative(sn):
            """Derivative of AASHTO equation for Newton-Raphson"""
            if sn <= 0:
                return 1.0
            
            # d/dSN of the equation
            term2_deriv = 9.36 / ((sn + 1) * math.log(10))
            
            psi_ratio = inputs.serviceability_loss / (4.2 - 1.5)
            if psi_ratio <= 0:
                psi_ratio = 0.01
            
            term3_num = math.log10(psi_ratio)
            term3_den = 0.40 + 1094 / ((sn + 1) ** 5.19)
            term3_den_deriv = -1094 * 5.19 / ((sn + 1) ** 6.19)
            
            term3_deriv = -term3_num * term3_den_deriv / (term3_den ** 2)
            
            return -(term2_deriv + term3_deriv)
        
        # Newton-Raphson iteration
        sn = 3.0  # Initial guess
        tolerance = 1e-6
        max_iterations = 100
        
        for i in range(max_iterations):
            f_sn = aashto_equation(sn)
            f_prime_sn = aashto_derivative(sn)
            
            if abs(f_sn) < tolerance:
                break
            
            if abs(f_prime_sn) < 1e-12:
                break
            
            sn_new = sn - f_sn / f_prime_sn
            
            if sn_new <= 0:
                sn_new = sn / 2
            
            if abs(sn_new - sn) < tolerance:
                break
            
            sn = sn_new
        
        return max(0.1, sn)  # Ensure positive result
    
    def distribute_structural_number(self, sn: float, inputs: DesignInputs) -> LayerThickness:
        """Distribute structural number among layers and calculate thicknesses"""
        
        # Calculate layer coefficients if not provided
        a1 = inputs.surface_coefficient or self.calculate_layer_coefficient('surface', 'dense_graded_ac')
        a2 = inputs.binder_coefficient or self.calculate_layer_coefficient('binder', 'dense_graded_ac')
        a3 = inputs.base_coefficient or self.calculate_layer_coefficient('base', 'crushed_stone')
        
        # Minimum thicknesses (inches)
        min_surface = 3.0  # 3 inches minimum
        min_binder = 4.0   # 4 inches minimum
        min_base = 6.0     # 6 inches minimum
        
        # Calculate required thicknesses
        # Start with minimum surface thickness
        d1 = min_surface
        sn1 = a1 * d1
        
        # Remaining SN for binder and base
        remaining_sn = sn - sn1
        
        if remaining_sn <= 0:
            # Surface layer is sufficient
            d2 = 0
            d3 = 0
        else:
            # Add binder layer
            d2 = min_binder
            sn2 = a2 * d2 * inputs.binder_drainage
            
            remaining_sn -= sn2
            
            if remaining_sn <= 0:
                # Surface and binder are sufficient
                d3 = 0
            else:
                # Calculate base thickness
                d3 = remaining_sn / (a3 * inputs.base_drainage)
                d3 = max(d3, min_base)
        
        # Convert from inches to centimeters
        surface_cm = d1 * 2.54
        binder_cm = d2 * 2.54
        base_cm = d3 * 2.54
        total_cm = surface_cm + binder_cm + base_cm
        
        return LayerThickness(
            surface=round(surface_cm, 1),
            binder=round(binder_cm, 1),
            base=round(base_cm, 1),
            total=round(total_cm, 1)
        )
    
    def validate_inputs(self, inputs: DesignInputs) -> Tuple[List[str], List[str]]:
        """Validate design inputs and return warnings and errors"""
        warnings = []
        errors = []
        
        # Check for errors
        if inputs.w18 <= 0:
            errors.append("عدد المحاور المكافئة W18 يجب أن يكون أكبر من صفر")
        
        if not (0 < inputs.reliability < 100):
            errors.append("معامل الاعتمادية يجب أن يكون بين 0 و 100")
        
        if inputs.serviceability_loss <= 0:
            errors.append("معامل الخدمة يجب أن يكون أكبر من صفر")
        
        if inputs.standard_deviation <= 0:
            errors.append("الانحراف القياسي يجب أن يكون أكبر من صفر")
        
        if inputs.subgrade_cbr <= 0:
            errors.append("قيمة CBR يجب أن يكون أكبر من صفر")
        
        # Check for warnings
        if inputs.w18 > 1e8:
            warnings.append("عدد المحاور المكافئة مرتفع جداً - تأكد من صحة القيمة")
        
        if inputs.reliability > 99:
            warnings.append("معامل اعتمادية عالي جداً قد يؤدي إلى تصميم مكلف")
        
        if inputs.serviceability_loss > 3.0:
            warnings.append("معامل الخدمة مرتفع - قد يؤثر على جودة الطريق")
        
        if inputs.subgrade_cbr < 2:
            warnings.append("قيمة CBR منخفضة جداً - قد تحتاج لتحسين التربة")
        
        if inputs.subgrade_cbr > 30:
            warnings.append("قيمة CBR مرتفعة جداً - تأكد من صحة القيمة")
        
        return warnings, errors
    
    def calculate(self, inputs: DesignInputs) -> DesignResults:
        """Perform complete AASHTO 1993 design calculation"""
        
        # Validate inputs
        warnings, errors = self.validate_inputs(inputs)
        
        if errors:
            return DesignResults(
                structural_number=0.0,
                layer_thickness=LayerThickness(0, 0, 0, 0),
                inputs=inputs,
                subgrade_modulus=0.0,
                reliability_factor=0.0,
                is_valid=False,
                warnings=warnings,
                errors=errors
            )
        
        try:
            # Calculate structural number
            sn = self.calculate_structural_number(inputs)
            
            # Distribute among layers
            thickness = self.distribute_structural_number(sn, inputs)
            
            # Calculate additional values
            mr = self.calculate_subgrade_modulus(inputs.subgrade_cbr)
            zr = self.calculate_reliability_factor(inputs.reliability)
            
            # Create results
            results = DesignResults(
                structural_number=round(sn, 3),
                layer_thickness=thickness,
                inputs=inputs,
                subgrade_modulus=round(mr, 0),
                reliability_factor=round(zr, 3),
                is_valid=True,
                warnings=warnings,
                errors=[]
            )
            
            self.last_calculation = results
            return results
            
        except Exception as e:
            errors.append(f"خطأ في الحساب: {str(e)}")
            return DesignResults(
                structural_number=0.0,
                layer_thickness=LayerThickness(0, 0, 0, 0),
                inputs=inputs,
                subgrade_modulus=0.0,
                reliability_factor=0.0,
                is_valid=False,
                warnings=warnings,
                errors=errors
            )
