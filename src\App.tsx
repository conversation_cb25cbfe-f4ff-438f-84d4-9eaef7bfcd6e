import { useState } from 'react';
import {
  App<PERSON><PERSON>,
  Box,
  Container,
  IconButton,
  ThemeProvider,
  Toolbar,
  Typography,
  createTheme,
  useTheme,
} from '@mui/material';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import TranslateIcon from '@mui/icons-material/Translate';
import { useTranslation } from 'react-i18next';
import InputForm from './components/InputForm';
import ThreeDVisualization from './components/ThreeDVisualization';
import Analysis from './components/Analysis';
import Results from './components/Results';

function App() {
  const [darkMode, setDarkMode] = useState(false);
  const { t, i18n } = useTranslation();
  const theme = useTheme();

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  const toggleLanguage = () => {
    i18n.changeLanguage(i18n.language === 'ar' ? 'en' : 'ar');
  };

  const customTheme = createTheme({
    ...theme,
    palette: {
      mode: darkMode ? 'dark' : 'light',
    },
    direction: i18n.language === 'ar' ? 'rtl' : 'ltr',
  });

  return (
    <ThemeProvider theme={customTheme}>
      <Box sx={{ flexGrow: 1 }}>
        <AppBar position="static">
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              {t('app.title')}
            </Typography>
            <IconButton onClick={toggleLanguage} color="inherit">
              <TranslateIcon />
            </IconButton>
            <IconButton onClick={toggleDarkMode} color="inherit">
              {darkMode ? <Brightness7Icon /> : <Brightness4Icon />}
            </IconButton>
          </Toolbar>
        </AppBar>

        <Container maxWidth="lg" sx={{ mt: 4 }}>
          <InputForm />
          <ThreeDVisualization />
          <Analysis />
          <Results />
        </Container>

        {/* Copyright Info */}
        <Box sx={{ textAlign: 'center', mt: 4, mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            🔒 تم تصميم وتطوير فكرة البرنامج بواسطة:
            <br />
            المهندس محمد يونس الجوالي
            <br />
            📞 WhatsApp: +964 750 781 2241
            <br />
            🌐 Facebook: facebook.com/aljwalei
            <br />
            📸 Instagram: instagram.com/m2n.9
            <br />
            جميع الحقوق محفوظة ©
          </Typography>
        </Box>
      </Box>
    </ThemeProvider>
  );
}

export default App;
