#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple launcher for Asphalto application
This file can be used to run the application during development
"""

import sys
import os

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import and run the main application
if __name__ == '__main__':
    try:
        from main import main
        main()
    except ImportError as e:
        print(f"Error importing main module: {e}")
        print("Make sure all required packages are installed:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"Error running application: {e}")
        sys.exit(1)
