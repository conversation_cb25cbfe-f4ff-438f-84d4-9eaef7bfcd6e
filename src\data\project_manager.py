#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Project Management System for Asphalto
Handles saving, loading, and managing design projects
"""

import json
import os
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import uuid

from src.core.aashto_calculator import DesignInputs, DesignResults

@dataclass
class ProjectInfo:
    """Project information"""
    id: str
    name: str
    description: str
    created_date: str
    modified_date: str
    version: str = "1.0"
    author: str = ""
    location: str = ""
    road_type: str = ""

@dataclass
class Project:
    """Complete project data"""
    info: ProjectInfo
    inputs: DesignInputs
    results: Optional[DesignResults] = None
    notes: str = ""
    custom_materials: Dict[str, Any] = None

class ProjectManager:
    """Project management system"""
    
    def __init__(self, projects_dir: str):
        """Initialize project manager"""
        self.projects_dir = projects_dir
        self.current_project = None
        self.auto_save_enabled = True
        
        # Ensure projects directory exists
        os.makedirs(projects_dir, exist_ok=True)
    
    def create_new_project(self, name: str, description: str = "", 
                          author: str = "", location: str = "", 
                          road_type: str = "") -> Project:
        """Create a new project"""
        
        # Create default inputs
        default_inputs = DesignInputs(
            w18=1000000,
            reliability=95.0,
            serviceability_loss=2.0,
            standard_deviation=0.45,
            design_period=20,
            surface_modulus=450000,
            binder_modulus=400000,
            base_modulus=30000,
            subgrade_cbr=6.0
        )
        
        # Create project info
        project_id = str(uuid.uuid4())
        current_time = datetime.now().isoformat()
        
        project_info = ProjectInfo(
            id=project_id,
            name=name,
            description=description,
            created_date=current_time,
            modified_date=current_time,
            author=author,
            location=location,
            road_type=road_type
        )
        
        # Create project
        project = Project(
            info=project_info,
            inputs=default_inputs,
            custom_materials={}
        )
        
        self.current_project = project
        return project
    
    def save_project(self, project: Project, file_path: Optional[str] = None) -> bool:
        """Save project to file"""
        try:
            if file_path is None:
                # Generate default file path
                safe_name = "".join(c for c in project.info.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                file_path = os.path.join(self.projects_dir, f"{safe_name}_{project.info.id[:8]}.asphalto")
            
            # Update modified date
            project.info.modified_date = datetime.now().isoformat()
            
            # Convert project to dictionary
            project_dict = {
                'info': asdict(project.info),
                'inputs': asdict(project.inputs),
                'results': asdict(project.results) if project.results else None,
                'notes': project.notes,
                'custom_materials': project.custom_materials or {}
            }
            
            # Save to file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(project_dict, f, indent=2, ensure_ascii=False)
            
            # Create backup if enabled
            if self.auto_save_enabled:
                self._create_backup(file_path)
            
            return True
            
        except Exception as e:
            print(f"Error saving project: {e}")
            return False
    
    def load_project(self, file_path: str) -> Optional[Project]:
        """Load project from file"""
        try:
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                project_dict = json.load(f)
            
            # Create project info
            info_dict = project_dict.get('info', {})
            project_info = ProjectInfo(**info_dict)
            
            # Create design inputs
            inputs_dict = project_dict.get('inputs', {})
            design_inputs = DesignInputs(**inputs_dict)
            
            # Create design results if available
            results_dict = project_dict.get('results')
            design_results = None
            if results_dict:
                # Reconstruct DesignResults (this is complex due to nested dataclasses)
                # For now, we'll set it to None and recalculate if needed
                pass
            
            # Create project
            project = Project(
                info=project_info,
                inputs=design_inputs,
                results=design_results,
                notes=project_dict.get('notes', ''),
                custom_materials=project_dict.get('custom_materials', {})
            )
            
            self.current_project = project
            return project
            
        except Exception as e:
            print(f"Error loading project: {e}")
            return None
    
    def get_project_list(self) -> List[Dict[str, Any]]:
        """Get list of available projects"""
        projects = []
        
        try:
            for filename in os.listdir(self.projects_dir):
                if filename.endswith('.asphalto'):
                    file_path = os.path.join(self.projects_dir, filename)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            project_dict = json.load(f)
                        
                        info = project_dict.get('info', {})
                        projects.append({
                            'file_path': file_path,
                            'name': info.get('name', 'Unnamed Project'),
                            'description': info.get('description', ''),
                            'created_date': info.get('created_date', ''),
                            'modified_date': info.get('modified_date', ''),
                            'author': info.get('author', ''),
                            'location': info.get('location', ''),
                            'road_type': info.get('road_type', '')
                        })
                        
                    except Exception as e:
                        print(f"Error reading project {filename}: {e}")
                        continue
            
            # Sort by modified date (newest first)
            projects.sort(key=lambda x: x.get('modified_date', ''), reverse=True)
            
        except Exception as e:
            print(f"Error getting project list: {e}")
        
        return projects
    
    def delete_project(self, file_path: str) -> bool:
        """Delete project file"""
        try:
            if os.path.exists(file_path):
                # Move to trash/backup before deleting
                backup_path = self._get_backup_path(file_path)
                shutil.move(file_path, backup_path)
                return True
            return False
            
        except Exception as e:
            print(f"Error deleting project: {e}")
            return False
    
    def duplicate_project(self, source_path: str, new_name: str) -> Optional[str]:
        """Duplicate an existing project"""
        try:
            project = self.load_project(source_path)
            if not project:
                return None
            
            # Update project info
            project.info.id = str(uuid.uuid4())
            project.info.name = new_name
            project.info.created_date = datetime.now().isoformat()
            project.info.modified_date = datetime.now().isoformat()
            
            # Generate new file path
            safe_name = "".join(c for c in new_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            new_path = os.path.join(self.projects_dir, f"{safe_name}_{project.info.id[:8]}.asphalto")
            
            # Save duplicated project
            if self.save_project(project, new_path):
                return new_path
            
            return None
            
        except Exception as e:
            print(f"Error duplicating project: {e}")
            return None
    
    def export_project(self, project: Project, export_path: str, 
                      format: str = 'json') -> bool:
        """Export project to different formats"""
        try:
            if format.lower() == 'json':
                return self.save_project(project, export_path)
            
            elif format.lower() == 'excel':
                return self._export_to_excel(project, export_path)
            
            elif format.lower() == 'csv':
                return self._export_to_csv(project, export_path)
            
            return False
            
        except Exception as e:
            print(f"Error exporting project: {e}")
            return False
    
    def _export_to_excel(self, project: Project, file_path: str) -> bool:
        """Export project to Excel format"""
        try:
            import pandas as pd
            
            # Create data for Excel export
            data = {
                'Parameter': [],
                'Value': [],
                'Unit': [],
                'Description': []
            }
            
            # Add project info
            data['Parameter'].extend(['Project Name', 'Description', 'Author', 'Location'])
            data['Value'].extend([project.info.name, project.info.description, 
                                project.info.author, project.info.location])
            data['Unit'].extend(['', '', '', ''])
            data['Description'].extend(['', '', '', ''])
            
            # Add design inputs
            inputs = project.inputs
            data['Parameter'].extend([
                'W18 (18-kip ESALs)', 'Reliability', 'Serviceability Loss', 
                'Standard Deviation', 'Design Period', 'Subgrade CBR'
            ])
            data['Value'].extend([
                inputs.w18, inputs.reliability, inputs.serviceability_loss,
                inputs.standard_deviation, inputs.design_period, inputs.subgrade_cbr
            ])
            data['Unit'].extend(['', '%', '', '', 'years', '%'])
            data['Description'].extend(['', '', 'ΔPSI', 'S0', '', ''])
            
            # Add results if available
            if project.results:
                results = project.results
                data['Parameter'].extend([
                    'Structural Number', 'Surface Thickness', 'Binder Thickness',
                    'Base Thickness', 'Total Thickness'
                ])
                data['Value'].extend([
                    results.structural_number, results.layer_thickness.surface,
                    results.layer_thickness.binder, results.layer_thickness.base,
                    results.layer_thickness.total
                ])
                data['Unit'].extend(['', 'cm', 'cm', 'cm', 'cm'])
                data['Description'].extend(['SN', '', '', '', ''])
            
            # Create DataFrame and save
            df = pd.DataFrame(data)
            df.to_excel(file_path, index=False, engine='openpyxl')
            
            return True
            
        except Exception as e:
            print(f"Error exporting to Excel: {e}")
            return False
    
    def _export_to_csv(self, project: Project, file_path: str) -> bool:
        """Export project to CSV format"""
        try:
            import csv
            
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                
                # Write headers
                writer.writerow(['Parameter', 'Value', 'Unit', 'Description'])
                
                # Write project info
                writer.writerow(['Project Name', project.info.name, '', ''])
                writer.writerow(['Description', project.info.description, '', ''])
                writer.writerow(['Author', project.info.author, '', ''])
                writer.writerow(['Location', project.info.location, '', ''])
                
                # Write design inputs
                inputs = project.inputs
                writer.writerow(['W18 (18-kip ESALs)', inputs.w18, '', ''])
                writer.writerow(['Reliability', inputs.reliability, '%', ''])
                writer.writerow(['Serviceability Loss', inputs.serviceability_loss, '', 'ΔPSI'])
                writer.writerow(['Standard Deviation', inputs.standard_deviation, '', 'S0'])
                writer.writerow(['Design Period', inputs.design_period, 'years', ''])
                writer.writerow(['Subgrade CBR', inputs.subgrade_cbr, '%', ''])
                
                # Write results if available
                if project.results:
                    results = project.results
                    writer.writerow(['Structural Number', results.structural_number, '', 'SN'])
                    writer.writerow(['Surface Thickness', results.layer_thickness.surface, 'cm', ''])
                    writer.writerow(['Binder Thickness', results.layer_thickness.binder, 'cm', ''])
                    writer.writerow(['Base Thickness', results.layer_thickness.base, 'cm', ''])
                    writer.writerow(['Total Thickness', results.layer_thickness.total, 'cm', ''])
            
            return True
            
        except Exception as e:
            print(f"Error exporting to CSV: {e}")
            return False
    
    def _create_backup(self, file_path: str):
        """Create backup of project file"""
        try:
            backup_dir = os.path.join(os.path.dirname(self.projects_dir), 'backups')
            os.makedirs(backup_dir, exist_ok=True)
            
            filename = os.path.basename(file_path)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{timestamp}_{filename}"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            shutil.copy2(file_path, backup_path)
            
            # Clean old backups (keep only last 10)
            self._cleanup_old_backups(backup_dir)
            
        except Exception as e:
            print(f"Error creating backup: {e}")
    
    def _get_backup_path(self, file_path: str) -> str:
        """Get backup path for a file"""
        backup_dir = os.path.join(os.path.dirname(self.projects_dir), 'backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        filename = os.path.basename(file_path)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"deleted_{timestamp}_{filename}"
        
        return os.path.join(backup_dir, backup_filename)
    
    def _cleanup_old_backups(self, backup_dir: str, max_backups: int = 10):
        """Clean up old backup files"""
        try:
            backup_files = []
            for filename in os.listdir(backup_dir):
                if filename.endswith('.asphalto'):
                    file_path = os.path.join(backup_dir, filename)
                    backup_files.append((file_path, os.path.getmtime(file_path)))
            
            # Sort by modification time (oldest first)
            backup_files.sort(key=lambda x: x[1])
            
            # Remove old backups
            while len(backup_files) > max_backups:
                old_file = backup_files.pop(0)
                os.remove(old_file[0])
                
        except Exception as e:
            print(f"Error cleaning up backups: {e}")
    
    def auto_save_project(self, project: Project):
        """Auto-save current project"""
        if self.auto_save_enabled and project:
            # Create auto-save file
            auto_save_dir = os.path.join(self.projects_dir, '.autosave')
            os.makedirs(auto_save_dir, exist_ok=True)
            
            auto_save_path = os.path.join(auto_save_dir, f"autosave_{project.info.id}.asphalto")
            self.save_project(project, auto_save_path)
    
    def recover_auto_save(self) -> List[str]:
        """Get list of auto-save files for recovery"""
        auto_save_files = []
        auto_save_dir = os.path.join(self.projects_dir, '.autosave')
        
        if os.path.exists(auto_save_dir):
            for filename in os.listdir(auto_save_dir):
                if filename.startswith('autosave_') and filename.endswith('.asphalto'):
                    auto_save_files.append(os.path.join(auto_save_dir, filename))
        
        return auto_save_files
