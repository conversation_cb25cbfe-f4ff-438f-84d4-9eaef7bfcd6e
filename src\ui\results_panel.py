#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Results Panel for Asphalto Application
Displays calculation results and analysis
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QGroupBox, QLabel, QPushButton, QTextEdit,
                            QFrame, QScrollArea, QProgressBar, QTableWidget,
                            QTableWidgetItem, QHeaderView, QSplitter)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor, QPen

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np

from src.core.aashto_calculator import DesignResults
from src.core.config import Config

class ResultsPanel(QWidget):
    """Results panel for displaying calculation results"""
    
    # Signals
    export_requested = pyqtSignal()
    
    def __init__(self, config: Config):
        super().__init__()
        
        self.config = config
        self.current_results = None
        
        self.init_ui()
    
    def init_ui(self):
        """Initialize user interface"""
        # Create scroll area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # Main widget inside scroll area
        main_widget = QWidget()
        scroll.setWidget(main_widget)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll)
        
        # Content layout
        layout = QVBoxLayout(main_widget)
        layout.setSpacing(10)
        
        # Title
        title_label = QLabel("نتائج التصميم")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        layout.addWidget(title_label)
        
        # Create splitter for results and visualization
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # Results summary group
        self.create_summary_group(splitter)
        
        # Layer thickness visualization
        self.create_visualization_group(splitter)
        
        # Detailed results table
        self.create_details_table(splitter)
        
        # Warnings and errors
        self.create_messages_group(splitter)
        
        # Export button
        self.create_export_button(layout)
        
        # Set splitter proportions
        splitter.setSizes([200, 300, 200, 100])
    
    def create_summary_group(self, parent):
        """Create results summary group"""
        group = QGroupBox("ملخص النتائج")
        group.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout = QFormLayout(group)
        
        # Structural Number
        self.sn_label = QLabel("--")
        self.sn_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        self.sn_label.setStyleSheet("color: #2980b9;")
        layout.addRow("الرقم الهيكلي (SN):", self.sn_label)
        
        # Total thickness
        self.total_thickness_label = QLabel("--")
        self.total_thickness_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        self.total_thickness_label.setStyleSheet("color: #27ae60;")
        layout.addRow("السماكة الكلية:", self.total_thickness_label)
        
        # Surface thickness
        self.surface_thickness_label = QLabel("--")
        layout.addRow("سماكة الطبقة السطحية:", self.surface_thickness_label)
        
        # Binder thickness
        self.binder_thickness_label = QLabel("--")
        layout.addRow("سماكة الطبقة الرابطة:", self.binder_thickness_label)
        
        # Base thickness
        self.base_thickness_label = QLabel("--")
        layout.addRow("سماكة طبقة الأساس:", self.base_thickness_label)
        
        # Subgrade modulus
        self.subgrade_modulus_label = QLabel("--")
        layout.addRow("معامل مرونة التربة التحتية:", self.subgrade_modulus_label)
        
        parent.addWidget(group)
    
    def create_visualization_group(self, parent):
        """Create layer thickness visualization"""
        group = QGroupBox("مقطع عرضي للطبقات")
        group.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout = QVBoxLayout(group)
        
        # Create matplotlib figure
        self.figure = Figure(figsize=(8, 4), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # Create initial empty plot
        self.ax = self.figure.add_subplot(111)
        self.ax.set_title('مقطع عرضي لطبقات الرصف', fontsize=12, fontweight='bold')
        self.ax.set_xlabel('العرض (م)')
        self.ax.set_ylabel('العمق (سم)')
        
        # Set Arabic font for matplotlib
        plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
        
        parent.addWidget(group)
    
    def create_details_table(self, parent):
        """Create detailed results table"""
        group = QGroupBox("تفاصيل النتائج")
        group.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout = QVBoxLayout(group)
        
        # Create table
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(3)
        self.details_table.setHorizontalHeaderLabels(["المعامل", "القيمة", "الوحدة"])
        
        # Set table properties
        header = self.details_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        
        self.details_table.setAlternatingRowColors(True)
        self.details_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(self.details_table)
        parent.addWidget(group)
    
    def create_messages_group(self, parent):
        """Create warnings and errors group"""
        group = QGroupBox("رسائل وتحذيرات")
        group.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout = QVBoxLayout(group)
        
        # Messages text area
        self.messages_text = QTextEdit()
        self.messages_text.setMaximumHeight(100)
        self.messages_text.setReadOnly(True)
        layout.addWidget(self.messages_text)
        
        parent.addWidget(group)
    
    def create_export_button(self, parent_layout):
        """Create export button"""
        button_layout = QHBoxLayout()
        
        self.export_btn = QPushButton("تصدير التقرير")
        self.export_btn.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.export_btn.setMinimumHeight(40)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.export_btn.clicked.connect(self.export_requested.emit)
        self.export_btn.setEnabled(False)
        
        button_layout.addStretch()
        button_layout.addWidget(self.export_btn)
        button_layout.addStretch()
        
        parent_layout.addLayout(button_layout)
    
    def display_results(self, results: DesignResults):
        """Display calculation results"""
        self.current_results = results
        
        if not results.is_valid:
            self.display_errors(results)
            return
        
        # Update summary labels
        self.sn_label.setText(f"{results.structural_number:.3f}")
        self.total_thickness_label.setText(f"{results.layer_thickness.total:.1f} سم")
        self.surface_thickness_label.setText(f"{results.layer_thickness.surface:.1f} سم")
        self.binder_thickness_label.setText(f"{results.layer_thickness.binder:.1f} سم")
        self.base_thickness_label.setText(f"{results.layer_thickness.base:.1f} سم")
        self.subgrade_modulus_label.setText(f"{results.subgrade_modulus:.0f} psi")
        
        # Update visualization
        self.update_visualization(results)
        
        # Update details table
        self.update_details_table(results)
        
        # Update messages
        self.update_messages(results)
        
        # Enable export button
        self.export_btn.setEnabled(True)
    
    def display_errors(self, results: DesignResults):
        """Display calculation errors"""
        # Clear all displays
        self.clear_results()
        
        # Show errors
        error_text = "أخطاء في الحساب:\n"
        for error in results.errors:
            error_text += f"• {error}\n"
        
        if results.warnings:
            error_text += "\nتحذيرات:\n"
            for warning in results.warnings:
                error_text += f"• {warning}\n"
        
        self.messages_text.setPlainText(error_text)
        self.messages_text.setStyleSheet("color: #e74c3c;")
    
    def update_visualization(self, results: DesignResults):
        """Update layer thickness visualization"""
        self.ax.clear()
        
        # Layer data
        layers = [
            ("الطبقة السطحية", results.layer_thickness.surface, "#2c3e50"),
            ("الطبقة الرابطة", results.layer_thickness.binder, "#34495e"),
            ("طبقة الأساس", results.layer_thickness.base, "#7f8c8d"),
            ("التربة التحتية", 50, "#95a5a6")  # Fixed height for visualization
        ]
        
        # Calculate positions
        y_pos = 0
        width = 10  # Road width in meters
        
        for i, (name, thickness, color) in enumerate(layers):
            if thickness > 0:
                # Draw layer rectangle
                rect = plt.Rectangle((0, y_pos), width, thickness, 
                                   facecolor=color, edgecolor='black', linewidth=1)
                self.ax.add_patch(rect)
                
                # Add layer label
                if thickness > 5:  # Only add label if layer is thick enough
                    self.ax.text(width/2, y_pos + thickness/2, 
                               f"{name}\n{thickness:.1f} سم", 
                               ha='center', va='center', fontsize=9, 
                               color='white', fontweight='bold')
                
                y_pos += thickness
        
        # Set plot properties
        self.ax.set_xlim(-1, width + 1)
        self.ax.set_ylim(0, y_pos + 10)
        self.ax.set_xlabel('العرض (م)')
        self.ax.set_ylabel('العمق (سم)')
        self.ax.set_title('مقطع عرضي لطبقات الرصف', fontsize=12, fontweight='bold')
        self.ax.grid(True, alpha=0.3)
        
        # Invert y-axis to show depth correctly
        self.ax.invert_yaxis()
        
        # Refresh canvas
        self.canvas.draw()
    
    def update_details_table(self, results: DesignResults):
        """Update detailed results table"""
        # Prepare data
        data = [
            ("الرقم الهيكلي (SN)", f"{results.structural_number:.3f}", ""),
            ("السماكة الكلية", f"{results.layer_thickness.total:.1f}", "سم"),
            ("سماكة الطبقة السطحية", f"{results.layer_thickness.surface:.1f}", "سم"),
            ("سماكة الطبقة الرابطة", f"{results.layer_thickness.binder:.1f}", "سم"),
            ("سماكة طبقة الأساس", f"{results.layer_thickness.base:.1f}", "سم"),
            ("معامل الاعتمادية (ZR)", f"{results.reliability_factor:.3f}", ""),
            ("معامل مرونة التربة التحتية", f"{results.subgrade_modulus:.0f}", "psi"),
            ("عدد المحاور المكافئة", f"{results.inputs.w18:.0f}", "ESAL"),
            ("الاعتمادية", f"{results.inputs.reliability:.1f}", "%"),
            ("فقدان الخدمة", f"{results.inputs.serviceability_loss:.1f}", ""),
            ("الانحراف المعياري", f"{results.inputs.standard_deviation:.2f}", ""),
            ("فترة التصميم", f"{results.inputs.design_period}", "سنة"),
            ("CBR التربة التحتية", f"{results.inputs.subgrade_cbr:.1f}", "%")
        ]
        
        # Set table size
        self.details_table.setRowCount(len(data))
        
        # Fill table
        for i, (param, value, unit) in enumerate(data):
            self.details_table.setItem(i, 0, QTableWidgetItem(param))
            self.details_table.setItem(i, 1, QTableWidgetItem(value))
            self.details_table.setItem(i, 2, QTableWidgetItem(unit))
            
            # Make items read-only
            for j in range(3):
                item = self.details_table.item(i, j)
                if item:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
    
    def update_messages(self, results: DesignResults):
        """Update messages area"""
        messages = []
        
        # Add warnings
        if results.warnings:
            messages.append("تحذيرات:")
            for warning in results.warnings:
                messages.append(f"• {warning}")
            messages.append("")
        
        # Add success message if no warnings
        if not results.warnings:
            messages.append("✓ تم حساب السماكة بنجاح")
            messages.append("✓ جميع المعاملات ضمن النطاق المقبول")
        
        # Add recommendations
        messages.append("توصيات:")
        
        # Check for thin layers
        if results.layer_thickness.surface < 7.5:  # Less than 3 inches
            messages.append("• يُنصح بزيادة سماكة الطبقة السطحية")
        
        if results.layer_thickness.base > 30:  # More than 12 inches
            messages.append("• طبقة الأساس سميكة، يُنصح بتحسين التربة التحتية")
        
        # Set text
        self.messages_text.setPlainText("\n".join(messages))
        
        # Set color based on warnings
        if results.warnings:
            self.messages_text.setStyleSheet("color: #f39c12;")
        else:
            self.messages_text.setStyleSheet("color: #27ae60;")
    
    def clear_results(self):
        """Clear all results displays"""
        # Clear summary labels
        labels = [
            self.sn_label, self.total_thickness_label, self.surface_thickness_label,
            self.binder_thickness_label, self.base_thickness_label, self.subgrade_modulus_label
        ]
        
        for label in labels:
            label.setText("--")
        
        # Clear visualization
        self.ax.clear()
        self.ax.set_title('مقطع عرضي لطبقات الرصف', fontsize=12, fontweight='bold')
        self.ax.set_xlabel('العرض (م)')
        self.ax.set_ylabel('العمق (سم)')
        self.canvas.draw()
        
        # Clear table
        self.details_table.setRowCount(0)
        
        # Clear messages
        self.messages_text.clear()
        self.messages_text.setStyleSheet("")
        
        # Disable export button
        self.export_btn.setEnabled(False)
        
        # Clear current results
        self.current_results = None
    
    def get_results_summary(self) -> str:
        """Get results summary as text"""
        if not self.current_results:
            return "لا توجد نتائج"
        
        results = self.current_results
        summary = f"""
نتائج تصميم الرصف الإسفلتي
========================

الرقم الهيكلي (SN): {results.structural_number:.3f}

سماكات الطبقات:
- الطبقة السطحية: {results.layer_thickness.surface:.1f} سم
- الطبقة الرابطة: {results.layer_thickness.binder:.1f} سم  
- طبقة الأساس: {results.layer_thickness.base:.1f} سم
- السماكة الكلية: {results.layer_thickness.total:.1f} سم

معاملات التصميم:
- عدد المحاور المكافئة: {results.inputs.w18:.0f} ESAL
- الاعتمادية: {results.inputs.reliability:.1f}%
- فقدان الخدمة: {results.inputs.serviceability_loss:.1f}
- CBR التربة التحتية: {results.inputs.subgrade_cbr:.1f}%
- معامل مرونة التربة التحتية: {results.subgrade_modulus:.0f} psi
"""
        
        if results.warnings:
            summary += "\nتحذيرات:\n"
            for warning in results.warnings:
                summary += f"- {warning}\n"
        
        return summary
