#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analysis Panel for Asphalto Application
Provides sensitivity analysis and design comparisons
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QGroupBox, QLabel, QPushButton, QComboBox,
                            QDoubleSpinBox, QSpinBox, QTabWidget, QFrame,
                            QScrollArea, QProgressBar, QCheckBox)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QFont

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np

from src.core.config import Config

class AnalysisPanel(QWidget):
    """Analysis panel for sensitivity analysis and comparisons"""
    
    # Signals
    analysis_requested = pyqtSignal(str)
    
    def __init__(self, config: Config):
        super().__init__()
        
        self.config = config
        self.current_results = None
        
        self.init_ui()
    
    def init_ui(self):
        """Initialize user interface"""
        # Main layout
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("التحليل والمقارنات")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        layout.addWidget(title_label)
        
        # Create tab widget for different analysis types
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Sensitivity analysis tab
        self.create_sensitivity_tab()
        
        # Time evaluation tab
        self.create_time_evaluation_tab()
        
        # Design comparison tab
        self.create_comparison_tab()
        
        # Performance analysis tab
        self.create_performance_tab()
    
    def create_sensitivity_tab(self):
        """Create sensitivity analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Controls group
        controls_group = QGroupBox("معاملات تحليل الحساسية")
        controls_layout = QFormLayout(controls_group)
        
        # Parameter selection
        self.sensitivity_parameter = QComboBox()
        self.sensitivity_parameter.addItems([
            "عدد المحاور المكافئة (W18)",
            "الاعتمادية (R)",
            "فقدان الخدمة (ΔPSI)",
            "CBR التربة التحتية",
            "معامل الطبقة السطحية",
            "معامل طبقة الأساس"
        ])
        controls_layout.addRow("المعامل المتغير:", self.sensitivity_parameter)
        
        # Range settings
        self.sensitivity_min = QDoubleSpinBox()
        self.sensitivity_min.setRange(-1000, 1000)
        self.sensitivity_min.setValue(0.5)
        controls_layout.addRow("الحد الأدنى (نسبة):", self.sensitivity_min)
        
        self.sensitivity_max = QDoubleSpinBox()
        self.sensitivity_max.setRange(-1000, 1000)
        self.sensitivity_max.setValue(2.0)
        controls_layout.addRow("الحد الأعلى (نسبة):", self.sensitivity_max)
        
        # Number of points
        self.sensitivity_points = QSpinBox()
        self.sensitivity_points.setRange(10, 100)
        self.sensitivity_points.setValue(20)
        controls_layout.addRow("عدد النقاط:", self.sensitivity_points)
        
        # Run button
        run_sensitivity_btn = QPushButton("تشغيل تحليل الحساسية")
        run_sensitivity_btn.clicked.connect(lambda: self.run_sensitivity_analysis())
        controls_layout.addRow("", run_sensitivity_btn)
        
        layout.addWidget(controls_group)
        
        # Results plot
        self.sensitivity_figure = Figure(figsize=(10, 6), dpi=100)
        self.sensitivity_canvas = FigureCanvas(self.sensitivity_figure)
        layout.addWidget(self.sensitivity_canvas)
        
        self.tab_widget.addTab(tab, "تحليل الحساسية")
    
    def create_time_evaluation_tab(self):
        """Create time evaluation tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Controls group
        controls_group = QGroupBox("معاملات التقييم الزمني")
        controls_layout = QFormLayout(controls_group)
        
        # Traffic growth rate
        self.traffic_growth = QDoubleSpinBox()
        self.traffic_growth.setRange(0, 10)
        self.traffic_growth.setDecimals(1)
        self.traffic_growth.setValue(3.0)
        self.traffic_growth.setSuffix(" %")
        controls_layout.addRow("معدل نمو الحركة المرورية:", self.traffic_growth)
        
        # Analysis period
        self.analysis_period = QSpinBox()
        self.analysis_period.setRange(10, 50)
        self.analysis_period.setValue(25)
        self.analysis_period.setSuffix(" سنة")
        controls_layout.addRow("فترة التحليل:", self.analysis_period)
        
        # Include maintenance
        self.include_maintenance = QCheckBox("تضمين أعمال الصيانة")
        self.include_maintenance.setChecked(True)
        controls_layout.addRow("", self.include_maintenance)
        
        # Run button
        run_time_btn = QPushButton("تشغيل التقييم الزمني")
        run_time_btn.clicked.connect(lambda: self.run_time_evaluation())
        controls_layout.addRow("", run_time_btn)
        
        layout.addWidget(controls_group)
        
        # Results plot
        self.time_figure = Figure(figsize=(10, 6), dpi=100)
        self.time_canvas = FigureCanvas(self.time_figure)
        layout.addWidget(self.time_canvas)
        
        self.tab_widget.addTab(tab, "التقييم الزمني")
    
    def create_comparison_tab(self):
        """Create design comparison tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Controls group
        controls_group = QGroupBox("مقارنة التصميمات")
        controls_layout = QVBoxLayout(controls_group)
        
        # Comparison scenarios
        scenarios_layout = QHBoxLayout()
        
        # Scenario 1
        scenario1_group = QGroupBox("السيناريو الأول")
        scenario1_layout = QFormLayout(scenario1_group)
        
        self.scenario1_reliability = QDoubleSpinBox()
        self.scenario1_reliability.setRange(50, 99.9)
        self.scenario1_reliability.setValue(90.0)
        scenario1_layout.addRow("الاعتمادية:", self.scenario1_reliability)
        
        self.scenario1_cbr = QDoubleSpinBox()
        self.scenario1_cbr.setRange(1, 100)
        self.scenario1_cbr.setValue(5.0)
        scenario1_layout.addRow("CBR:", self.scenario1_cbr)
        
        scenarios_layout.addWidget(scenario1_group)
        
        # Scenario 2
        scenario2_group = QGroupBox("السيناريو الثاني")
        scenario2_layout = QFormLayout(scenario2_group)
        
        self.scenario2_reliability = QDoubleSpinBox()
        self.scenario2_reliability.setRange(50, 99.9)
        self.scenario2_reliability.setValue(95.0)
        scenario2_layout.addRow("الاعتمادية:", self.scenario2_reliability)
        
        self.scenario2_cbr = QDoubleSpinBox()
        self.scenario2_cbr.setRange(1, 100)
        self.scenario2_cbr.setValue(8.0)
        scenario2_layout.addRow("CBR:", self.scenario2_cbr)
        
        scenarios_layout.addWidget(scenario2_group)
        
        controls_layout.addLayout(scenarios_layout)
        
        # Run button
        run_comparison_btn = QPushButton("مقارنة التصميمات")
        run_comparison_btn.clicked.connect(lambda: self.run_comparison())
        controls_layout.addWidget(run_comparison_btn)
        
        layout.addWidget(controls_group)
        
        # Results plot
        self.comparison_figure = Figure(figsize=(10, 6), dpi=100)
        self.comparison_canvas = FigureCanvas(self.comparison_figure)
        layout.addWidget(self.comparison_canvas)
        
        self.tab_widget.addTab(tab, "مقارنة التصميمات")
    
    def create_performance_tab(self):
        """Create performance analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Controls group
        controls_group = QGroupBox("تحليل الأداء")
        controls_layout = QFormLayout(controls_group)
        
        # Performance indicators
        self.performance_indicator = QComboBox()
        self.performance_indicator.addItems([
            "مؤشر الخدمة (PSI)",
            "التشقق (%)",
            "التخدد (mm)",
            "الخشونة الدولية (IRI)"
        ])
        controls_layout.addRow("مؤشر الأداء:", self.performance_indicator)
        
        # Analysis type
        self.analysis_type = QComboBox()
        self.analysis_type.addItems([
            "التدهور مع الزمن",
            "تأثير الأحمال",
            "تأثير المناخ",
            "فعالية الصيانة"
        ])
        controls_layout.addRow("نوع التحليل:", self.analysis_type)
        
        # Run button
        run_performance_btn = QPushButton("تشغيل تحليل الأداء")
        run_performance_btn.clicked.connect(lambda: self.run_performance_analysis())
        controls_layout.addRow("", run_performance_btn)
        
        layout.addWidget(controls_group)
        
        # Results plot
        self.performance_figure = Figure(figsize=(10, 6), dpi=100)
        self.performance_canvas = FigureCanvas(self.performance_figure)
        layout.addWidget(self.performance_canvas)
        
        self.tab_widget.addTab(tab, "تحليل الأداء")
    
    def run_sensitivity_analysis(self):
        """Run sensitivity analysis"""
        if not self.current_results:
            return
        
        # Get parameters
        parameter = self.sensitivity_parameter.currentText()
        min_factor = self.sensitivity_min.value()
        max_factor = self.sensitivity_max.value()
        num_points = self.sensitivity_points.value()
        
        # Create parameter range
        factors = np.linspace(min_factor, max_factor, num_points)
        
        # Simulate results (placeholder)
        base_thickness = self.current_results.layer_thickness.total
        thicknesses = []
        
        for factor in factors:
            # Simple simulation - in real implementation, would recalculate
            if "W18" in parameter:
                # W18 has logarithmic effect
                thickness = base_thickness * (1 + 0.1 * np.log(factor))
            elif "CBR" in parameter:
                # CBR has inverse effect
                thickness = base_thickness * (2 - factor) / 1.5
            else:
                # Linear effect for other parameters
                thickness = base_thickness * factor
            
            thicknesses.append(max(10, thickness))  # Minimum 10 cm
        
        # Plot results
        self.sensitivity_figure.clear()
        ax = self.sensitivity_figure.add_subplot(111)
        
        ax.plot(factors, thicknesses, 'b-', linewidth=2, marker='o', markersize=4)
        ax.axhline(y=base_thickness, color='r', linestyle='--', alpha=0.7, 
                  label=f'التصميم الأساسي ({base_thickness:.1f} سم)')
        
        ax.set_xlabel(f'معامل التغيير - {parameter}')
        ax.set_ylabel('السماكة الكلية (سم)')
        ax.set_title(f'تحليل حساسية - {parameter}')
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # Add annotations
        min_thickness = min(thicknesses)
        max_thickness = max(thicknesses)
        variation = ((max_thickness - min_thickness) / base_thickness) * 100
        
        ax.text(0.02, 0.98, f'التغيير: {variation:.1f}%', 
               transform=ax.transAxes, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        self.sensitivity_canvas.draw()
    
    def run_time_evaluation(self):
        """Run time evaluation analysis"""
        if not self.current_results:
            return
        
        # Get parameters
        growth_rate = self.traffic_growth.value() / 100
        period = self.analysis_period.value()
        include_maintenance = self.include_maintenance.isChecked()
        
        # Create time series
        years = np.arange(0, period + 1)
        
        # Calculate traffic growth
        initial_w18 = self.current_results.inputs.w18
        w18_over_time = initial_w18 * (1 + growth_rate) ** years
        
        # Calculate required thickness over time (simplified)
        base_thickness = self.current_results.layer_thickness.total
        required_thickness = []
        
        for w18 in w18_over_time:
            # Simplified relationship: thickness increases with log(W18)
            factor = np.log(w18 / initial_w18) * 0.1 + 1
            thickness = base_thickness * factor
            required_thickness.append(thickness)
        
        # Add maintenance effects
        if include_maintenance:
            maintenance_years = [10, 20]  # Maintenance at years 10 and 20
            for year in maintenance_years:
                if year < len(required_thickness):
                    # Maintenance reduces required thickness
                    for i in range(year, len(required_thickness)):
                        required_thickness[i] *= 0.95
        
        # Plot results
        self.time_figure.clear()
        ax = self.time_figure.add_subplot(111)
        
        ax.plot(years, required_thickness, 'b-', linewidth=2, label='السماكة المطلوبة')
        ax.axhline(y=base_thickness, color='r', linestyle='--', alpha=0.7, 
                  label=f'التصميم الأولي ({base_thickness:.1f} سم)')
        
        # Mark maintenance years
        if include_maintenance:
            for year in maintenance_years:
                if year <= period:
                    ax.axvline(x=year, color='g', linestyle=':', alpha=0.7)
                    ax.text(year, max(required_thickness) * 0.9, 'صيانة', 
                           rotation=90, ha='right', va='top')
        
        ax.set_xlabel('السنة')
        ax.set_ylabel('السماكة المطلوبة (سم)')
        ax.set_title('تقييم الأداء عبر الزمن')
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        self.time_canvas.draw()
    
    def run_comparison(self):
        """Run design comparison"""
        if not self.current_results:
            return
        
        # Get scenarios
        scenarios = [
            {
                'name': 'السيناريو الأول',
                'reliability': self.scenario1_reliability.value(),
                'cbr': self.scenario1_cbr.value(),
                'color': 'blue'
            },
            {
                'name': 'السيناريو الثاني', 
                'reliability': self.scenario2_reliability.value(),
                'cbr': self.scenario2_cbr.value(),
                'color': 'red'
            },
            {
                'name': 'التصميم الحالي',
                'reliability': self.current_results.inputs.reliability,
                'cbr': self.current_results.inputs.subgrade_cbr,
                'color': 'green'
            }
        ]
        
        # Calculate thicknesses for each scenario (simplified)
        base_thickness = self.current_results.layer_thickness.total
        base_reliability = self.current_results.inputs.reliability
        base_cbr = self.current_results.inputs.subgrade_cbr
        
        scenario_data = []
        for scenario in scenarios:
            # Simplified calculation
            rel_factor = scenario['reliability'] / base_reliability
            cbr_factor = base_cbr / scenario['cbr']
            thickness = base_thickness * rel_factor * cbr_factor
            
            scenario_data.append({
                'name': scenario['name'],
                'thickness': thickness,
                'color': scenario['color'],
                'reliability': scenario['reliability'],
                'cbr': scenario['cbr']
            })
        
        # Plot comparison
        self.comparison_figure.clear()
        ax = self.comparison_figure.add_subplot(111)
        
        names = [s['name'] for s in scenario_data]
        thicknesses = [s['thickness'] for s in scenario_data]
        colors = [s['color'] for s in scenario_data]
        
        bars = ax.bar(names, thicknesses, color=colors, alpha=0.7)
        
        # Add value labels on bars
        for bar, thickness in zip(bars, thicknesses):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                   f'{thickness:.1f} سم', ha='center', va='bottom')
        
        ax.set_ylabel('السماكة الكلية (سم)')
        ax.set_title('مقارنة سيناريوهات التصميم')
        ax.grid(True, alpha=0.3, axis='y')
        
        # Add scenario details as text
        details_text = ""
        for i, scenario in enumerate(scenario_data):
            details_text += f"{scenario['name']}: R={scenario['reliability']:.1f}%, CBR={scenario['cbr']:.1f}%\n"
        
        ax.text(0.02, 0.98, details_text.strip(), transform=ax.transAxes, 
               verticalalignment='top', fontsize=9,
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        self.comparison_canvas.draw()
    
    def run_performance_analysis(self):
        """Run performance analysis"""
        # Get parameters
        indicator = self.performance_indicator.currentText()
        analysis = self.analysis_type.currentText()
        
        # Generate sample data (placeholder)
        years = np.arange(0, 21)  # 20 years
        
        if "مؤشر الخدمة" in indicator:
            # PSI decreases over time
            values = 4.2 - 1.7 * (1 - np.exp(-years/8))
            ylabel = "مؤشر الخدمة (PSI)"
        elif "التشقق" in indicator:
            # Cracking increases over time
            values = 100 * (1 - np.exp(-years/10))
            ylabel = "التشقق (%)"
        elif "التخدد" in indicator:
            # Rutting increases over time
            values = 20 * (1 - np.exp(-years/12))
            ylabel = "التخدد (mm)"
        else:  # IRI
            # Roughness increases over time
            values = 1.5 + 3.5 * (1 - np.exp(-years/15))
            ylabel = "الخشونة الدولية (IRI)"
        
        # Plot results
        self.performance_figure.clear()
        ax = self.performance_figure.add_subplot(111)
        
        ax.plot(years, values, 'b-', linewidth=2, marker='o', markersize=4)
        
        # Add threshold lines if applicable
        if "مؤشر الخدمة" in indicator:
            ax.axhline(y=2.5, color='r', linestyle='--', alpha=0.7, label='الحد الأدنى المقبول')
        elif "التشقق" in indicator:
            ax.axhline(y=20, color='r', linestyle='--', alpha=0.7, label='حد التدخل')
        
        ax.set_xlabel('السنة')
        ax.set_ylabel(ylabel)
        ax.set_title(f'{indicator} - {analysis}')
        ax.grid(True, alpha=0.3)
        
        if ax.get_legend_handles_labels()[0]:
            ax.legend()
        
        self.performance_canvas.draw()
    
    def clear_analysis(self):
        """Clear all analysis plots"""
        # Clear all figures
        figures = [
            self.sensitivity_figure,
            self.time_figure, 
            self.comparison_figure,
            self.performance_figure
        ]
        
        canvases = [
            self.sensitivity_canvas,
            self.time_canvas,
            self.comparison_canvas, 
            self.performance_canvas
        ]
        
        for figure, canvas in zip(figures, canvases):
            figure.clear()
            canvas.draw()
        
        self.current_results = None
    
    def set_results(self, results):
        """Set current results for analysis"""
        self.current_results = results
