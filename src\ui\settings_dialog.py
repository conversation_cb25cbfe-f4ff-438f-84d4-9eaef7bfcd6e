#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Settings Dialog for Asphalto Application
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QGroupBox, QLabel, QPushButton, QComboBox,
                            QCheckBox, QSpinBox, QDoubleSpinBox, QLineEdit,
                            QFileDialog, QTabWidget, QWidget, QMessageBox,
                            QSlider, QFrame)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from src.core.config import Config

class SettingsDialog(QDialog):
    """Settings configuration dialog"""
    
    def __init__(self, config: Config, parent=None):
        super().__init__(parent)
        
        self.config = config
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("إعدادات البرنامج")
        self.setMinimumSize(500, 600)
        self.setModal(True)
        
        # Main layout
        layout = QVBoxLayout(self)
        
        # Create tab widget
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # General settings tab
        self.create_general_tab(tab_widget)
        
        # Calculation settings tab
        self.create_calculation_tab(tab_widget)
        
        # Display settings tab
        self.create_display_tab(tab_widget)
        
        # Advanced settings tab
        self.create_advanced_tab(tab_widget)
        
        # Buttons
        self.create_buttons(layout)
    
    def create_general_tab(self, tab_widget):
        """Create general settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Language settings
        lang_group = QGroupBox("إعدادات اللغة")
        lang_layout = QFormLayout(lang_group)
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        lang_layout.addRow("اللغة:", self.language_combo)
        
        layout.addWidget(lang_group)
        
        # Theme settings
        theme_group = QGroupBox("إعدادات المظهر")
        theme_layout = QFormLayout(theme_group)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["فاتح", "داكن"])
        theme_layout.addRow("المظهر:", self.theme_combo)
        
        layout.addWidget(theme_group)
        
        # Auto-save settings
        autosave_group = QGroupBox("الحفظ التلقائي")
        autosave_layout = QFormLayout(autosave_group)
        
        self.auto_save_check = QCheckBox("تفعيل الحفظ التلقائي")
        autosave_layout.addRow("", self.auto_save_check)
        
        self.backup_count = QSpinBox()
        self.backup_count.setRange(1, 20)
        self.backup_count.setValue(5)
        autosave_layout.addRow("عدد النسخ الاحتياطية:", self.backup_count)
        
        layout.addWidget(autosave_group)
        
        # File locations
        files_group = QGroupBox("مواقع الملفات")
        files_layout = QFormLayout(files_group)
        
        # Projects directory
        projects_layout = QHBoxLayout()
        self.projects_dir = QLineEdit()
        projects_layout.addWidget(self.projects_dir)
        
        browse_projects_btn = QPushButton("تصفح")
        browse_projects_btn.clicked.connect(self.browse_projects_dir)
        projects_layout.addWidget(browse_projects_btn)
        
        files_layout.addRow("مجلد المشاريع:", projects_layout)
        
        # Material library
        library_layout = QHBoxLayout()
        self.material_library = QLineEdit()
        library_layout.addWidget(self.material_library)
        
        browse_library_btn = QPushButton("تصفح")
        browse_library_btn.clicked.connect(self.browse_material_library)
        library_layout.addWidget(browse_library_btn)
        
        files_layout.addRow("مكتبة المواد:", library_layout)
        
        layout.addWidget(files_group)
        
        layout.addStretch()
        tab_widget.addTab(tab, "عام")
    
    def create_calculation_tab(self, tab_widget):
        """Create calculation settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Default values
        defaults_group = QGroupBox("القيم الافتراضية")
        defaults_layout = QFormLayout(defaults_group)
        
        self.default_reliability = QDoubleSpinBox()
        self.default_reliability.setRange(50, 99.9)
        self.default_reliability.setDecimals(1)
        self.default_reliability.setSuffix(" %")
        defaults_layout.addRow("الاعتمادية الافتراضية:", self.default_reliability)
        
        self.default_serviceability = QDoubleSpinBox()
        self.default_serviceability.setRange(1.0, 4.0)
        self.default_serviceability.setDecimals(1)
        defaults_layout.addRow("فقدان الخدمة الافتراضي:", self.default_serviceability)
        
        self.default_std_dev = QDoubleSpinBox()
        self.default_std_dev.setRange(0.1, 1.0)
        self.default_std_dev.setDecimals(2)
        defaults_layout.addRow("الانحراف المعياري الافتراضي:", self.default_std_dev)
        
        self.default_design_period = QSpinBox()
        self.default_design_period.setRange(10, 50)
        self.default_design_period.setSuffix(" سنة")
        defaults_layout.addRow("فترة التصميم الافتراضية:", self.default_design_period)
        
        layout.addWidget(defaults_group)
        
        # Calculation precision
        precision_group = QGroupBox("دقة الحسابات")
        precision_layout = QFormLayout(precision_group)
        
        self.precision_spinbox = QSpinBox()
        self.precision_spinbox.setRange(1, 5)
        self.precision_spinbox.setValue(2)
        precision_layout.addRow("عدد الخانات العشرية:", self.precision_spinbox)
        
        layout.addWidget(precision_group)
        
        # Units
        units_group = QGroupBox("الوحدات")
        units_layout = QFormLayout(units_group)
        
        self.units_combo = QComboBox()
        self.units_combo.addItems(["متري", "إمبراطوري"])
        units_layout.addRow("نظام الوحدات:", self.units_combo)
        
        layout.addWidget(units_group)
        
        # Validation settings
        validation_group = QGroupBox("التحقق من صحة البيانات")
        validation_layout = QFormLayout(validation_group)
        
        self.show_warnings = QCheckBox("عرض التحذيرات")
        validation_layout.addRow("", self.show_warnings)
        
        self.strict_validation = QCheckBox("التحقق الصارم")
        validation_layout.addRow("", self.strict_validation)
        
        layout.addWidget(validation_group)
        
        layout.addStretch()
        tab_widget.addTab(tab, "الحسابات")
    
    def create_display_tab(self, tab_widget):
        """Create display settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Interface settings
        interface_group = QGroupBox("إعدادات الواجهة")
        interface_layout = QFormLayout(interface_group)
        
        self.show_tooltips = QCheckBox("عرض التلميحات")
        interface_layout.addRow("", self.show_tooltips)
        
        self.educational_mode = QCheckBox("الوضع التعليمي افتراضياً")
        interface_layout.addRow("", self.educational_mode)
        
        self.show_splash = QCheckBox("عرض شاشة البداية")
        interface_layout.addRow("", self.show_splash)
        
        layout.addWidget(interface_group)
        
        # Chart settings
        chart_group = QGroupBox("إعدادات الرسوم البيانية")
        chart_layout = QFormLayout(chart_group)
        
        self.chart_style = QComboBox()
        self.chart_style.addItems(["كلاسيكي", "حديث", "ملون"])
        chart_layout.addRow("نمط الرسوم:", self.chart_style)
        
        self.chart_dpi = QSpinBox()
        self.chart_dpi.setRange(72, 300)
        self.chart_dpi.setValue(100)
        chart_layout.addRow("دقة الرسوم (DPI):", self.chart_dpi)
        
        layout.addWidget(chart_group)
        
        # Font settings
        font_group = QGroupBox("إعدادات الخط")
        font_layout = QFormLayout(font_group)
        
        self.font_size = QSpinBox()
        self.font_size.setRange(8, 20)
        self.font_size.setValue(10)
        font_layout.addRow("حجم الخط:", self.font_size)
        
        self.font_family = QComboBox()
        self.font_family.addItems(["Arial", "Tahoma", "Segoe UI", "Calibri"])
        font_layout.addRow("نوع الخط:", self.font_family)
        
        layout.addWidget(font_group)
        
        layout.addStretch()
        tab_widget.addTab(tab, "العرض")
    
    def create_advanced_tab(self, tab_widget):
        """Create advanced settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Performance settings
        performance_group = QGroupBox("إعدادات الأداء")
        performance_layout = QFormLayout(performance_group)
        
        self.enable_multithreading = QCheckBox("تفعيل المعالجة المتوازية")
        performance_layout.addRow("", self.enable_multithreading)
        
        self.cache_size = QSpinBox()
        self.cache_size.setRange(10, 1000)
        self.cache_size.setValue(100)
        self.cache_size.setSuffix(" MB")
        performance_layout.addRow("حجم الذاكرة المؤقتة:", self.cache_size)
        
        layout.addWidget(performance_group)
        
        # Debug settings
        debug_group = QGroupBox("إعدادات التطوير")
        debug_layout = QFormLayout(debug_group)
        
        self.debug_mode = QCheckBox("وضع التطوير")
        debug_layout.addRow("", self.debug_mode)
        
        self.log_level = QComboBox()
        self.log_level.addItems(["خطأ", "تحذير", "معلومات", "تفصيلي"])
        debug_layout.addRow("مستوى السجل:", self.log_level)
        
        layout.addWidget(debug_group)
        
        # Export settings
        export_group = QGroupBox("إعدادات التصدير")
        export_layout = QFormLayout(export_group)
        
        self.default_export_format = QComboBox()
        self.default_export_format.addItems(["PDF", "Excel", "Word"])
        export_layout.addRow("تنسيق التصدير الافتراضي:", self.default_export_format)
        
        self.include_charts = QCheckBox("تضمين الرسوم البيانية")
        export_layout.addRow("", self.include_charts)
        
        layout.addWidget(export_group)
        
        # Reset button
        reset_frame = QFrame()
        reset_layout = QHBoxLayout(reset_frame)
        reset_layout.addStretch()
        
        reset_btn = QPushButton("إعادة تعيين جميع الإعدادات")
        reset_btn.setStyleSheet("QPushButton { color: red; }")
        reset_btn.clicked.connect(self.reset_settings)
        reset_layout.addWidget(reset_btn)
        
        layout.addWidget(reset_frame)
        layout.addStretch()
        tab_widget.addTab(tab, "متقدم")
    
    def create_buttons(self, layout):
        """Create dialog buttons"""
        button_layout = QHBoxLayout()
        
        # OK button
        ok_btn = QPushButton("موافق")
        ok_btn.setDefault(True)
        ok_btn.clicked.connect(self.accept_settings)
        button_layout.addWidget(ok_btn)
        
        # Cancel button
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # Apply button
        apply_btn = QPushButton("تطبيق")
        apply_btn.clicked.connect(self.apply_settings)
        button_layout.addWidget(apply_btn)
        
        layout.addLayout(button_layout)
    
    def load_settings(self):
        """Load current settings into dialog"""
        # General settings
        language = self.config.get_language()
        self.language_combo.setCurrentText("العربية" if language == 'ar' else "English")
        
        theme = self.config.get_theme()
        self.theme_combo.setCurrentText("داكن" if theme == 'dark' else "فاتح")
        
        self.auto_save_check.setChecked(self.config.get('auto_save', True))
        self.backup_count.setValue(self.config.get('backup_count', 5))
        
        self.projects_dir.setText(self.config.get_projects_dir())
        self.material_library.setText(self.config.get_material_library_path())
        
        # Calculation settings
        self.default_reliability.setValue(self.config.get('default_reliability', 95.0))
        self.default_serviceability.setValue(self.config.get('default_serviceability_loss', 2.0))
        self.default_std_dev.setValue(self.config.get('default_standard_deviation', 0.45))
        self.default_design_period.setValue(self.config.get('default_design_period', 20))
        
        self.precision_spinbox.setValue(self.config.get('precision', 2))
        
        units = self.config.get('units', 'metric')
        self.units_combo.setCurrentText("متري" if units == 'metric' else "إمبراطوري")
        
        self.show_warnings.setChecked(self.config.get('show_warnings', True))
        self.strict_validation.setChecked(self.config.get('strict_validation', False))
        
        # Display settings
        self.show_tooltips.setChecked(self.config.get('show_tooltips', True))
        self.educational_mode.setChecked(self.config.get('show_educational_mode', True))
        self.show_splash.setChecked(self.config.get('show_splash', True))
        
        self.chart_style.setCurrentText(self.config.get('chart_style', 'حديث'))
        self.chart_dpi.setValue(self.config.get('chart_dpi', 100))
        
        self.font_size.setValue(self.config.get('font_size', 10))
        self.font_family.setCurrentText(self.config.get('font_family', 'Arial'))
        
        # Advanced settings
        self.enable_multithreading.setChecked(self.config.get('enable_multithreading', True))
        self.cache_size.setValue(self.config.get('cache_size', 100))
        
        self.debug_mode.setChecked(self.config.get('debug_mode', False))
        self.log_level.setCurrentText(self.config.get('log_level', 'معلومات'))
        
        self.default_export_format.setCurrentText(self.config.get('default_export_format', 'PDF'))
        self.include_charts.setChecked(self.config.get('include_charts', True))
    
    def apply_settings(self):
        """Apply settings without closing dialog"""
        # General settings
        language = 'ar' if self.language_combo.currentText() == "العربية" else 'en'
        self.config.set_language(language)
        
        theme = 'dark' if self.theme_combo.currentText() == "داكن" else 'light'
        self.config.set_theme(theme)
        
        self.config.set('auto_save', self.auto_save_check.isChecked())
        self.config.set('backup_count', self.backup_count.value())
        
        # Calculation settings
        self.config.set('default_reliability', self.default_reliability.value())
        self.config.set('default_serviceability_loss', self.default_serviceability.value())
        self.config.set('default_standard_deviation', self.default_std_dev.value())
        self.config.set('default_design_period', self.default_design_period.value())
        
        self.config.set('precision', self.precision_spinbox.value())
        
        units = 'metric' if self.units_combo.currentText() == "متري" else 'imperial'
        self.config.set('units', units)
        
        self.config.set('show_warnings', self.show_warnings.isChecked())
        self.config.set('strict_validation', self.strict_validation.isChecked())
        
        # Display settings
        self.config.set('show_tooltips', self.show_tooltips.isChecked())
        self.config.set('show_educational_mode', self.educational_mode.isChecked())
        self.config.set('show_splash', self.show_splash.isChecked())
        
        self.config.set('chart_style', self.chart_style.currentText())
        self.config.set('chart_dpi', self.chart_dpi.value())
        
        self.config.set('font_size', self.font_size.value())
        self.config.set('font_family', self.font_family.currentText())
        
        # Advanced settings
        self.config.set('enable_multithreading', self.enable_multithreading.isChecked())
        self.config.set('cache_size', self.cache_size.value())
        
        self.config.set('debug_mode', self.debug_mode.isChecked())
        self.config.set('log_level', self.log_level.currentText())
        
        self.config.set('default_export_format', self.default_export_format.currentText())
        self.config.set('include_charts', self.include_charts.isChecked())
    
    def accept_settings(self):
        """Apply settings and close dialog"""
        self.apply_settings()
        self.accept()
    
    def browse_projects_dir(self):
        """Browse for projects directory"""
        directory = QFileDialog.getExistingDirectory(
            self, 
            "اختيار مجلد المشاريع",
            self.projects_dir.text()
        )
        
        if directory:
            self.projects_dir.setText(directory)
    
    def browse_material_library(self):
        """Browse for material library file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار ملف مكتبة المواد",
            self.material_library.text(),
            "JSON Files (*.json);;All Files (*)"
        )
        
        if file_path:
            self.material_library.setText(file_path)
    
    def reset_settings(self):
        """Reset all settings to defaults"""
        reply = QMessageBox.question(
            self,
            "إعادة تعيين الإعدادات",
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.config.reset_to_defaults()
            self.load_settings()
            QMessageBox.information(self, "تم", "تم إعادة تعيين جميع الإعدادات")
