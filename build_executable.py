#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build script to create executable for Asphalto application using PyInstaller
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_dirs():
    """Clean previous build directories"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"Cleaning {dir_name}...")
            shutil.rmtree(dir_name)
    
    # Clean .spec files
    for spec_file in Path('.').glob('*.spec'):
        print(f"Removing {spec_file}...")
        spec_file.unlink()

def create_pyinstaller_spec():
    """Create PyInstaller spec file"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src/data/material_library.json', 'src/data'),
        ('assets', 'assets'),
        ('translations', 'translations'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'matplotlib.backends.backend_qt5agg',
        'numpy',
        'scipy',
        'pandas',
        'reportlab',
        'openpyxl',
        'xlsxwriter',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Asphalto',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icons/app_icon.ico' if os.path.exists('assets/icons/app_icon.ico') else None,
    version_file='version_info.txt' if os.path.exists('version_info.txt') else None,
)
'''
    
    with open('asphalto.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content.strip())
    
    print("Created asphalto.spec file")

def create_version_info():
    """Create version info file for Windows executable"""
    version_info = '''
# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Mohammed Younis Al-Jwalei'),
        StringStruct(u'FileDescription', u'Asphalto - Professional Asphalt Pavement Thickness Calculator'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'Asphalto'),
        StringStruct(u'LegalCopyright', u'Copyright © 2025 Mohammed Younis Al-Jwalei. All rights reserved.'),
        StringStruct(u'OriginalFilename', u'Asphalto.exe'),
        StringStruct(u'ProductName', u'Asphalto'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info.strip())
    
    print("Created version_info.txt file")

def build_executable():
    """Build the executable using PyInstaller"""
    print("Building executable with PyInstaller...")
    
    try:
        # Run PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'asphalto.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Build successful!")
            print(f"Executable created in: {os.path.abspath('dist')}")
            
            # List created files
            dist_dir = Path('dist')
            if dist_dir.exists():
                print("\nCreated files:")
                for file in dist_dir.rglob('*'):
                    if file.is_file():
                        size = file.stat().st_size / (1024 * 1024)  # MB
                        print(f"  {file.relative_to(dist_dir)} ({size:.1f} MB)")
        else:
            print("❌ Build failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error during build: {e}")
        return False
    
    return True

def create_installer_script():
    """Create NSIS installer script for Windows"""
    nsis_script = '''
; Asphalto Installer Script
; Created with NSIS

!define APP_NAME "Asphalto"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "Mohammed Younis Al-Jwalei"
!define APP_URL "https://github.com/aljwalei/asphalto"
!define APP_DESCRIPTION "Professional Asphalt Pavement Thickness Calculator"

; Main installer attributes
Name "${APP_NAME} ${APP_VERSION}"
OutFile "Asphalto_Setup_${APP_VERSION}.exe"
InstallDir "$PROGRAMFILES\\${APP_NAME}"
InstallDirRegKey HKLM "Software\\${APP_NAME}" "InstallDir"
RequestExecutionLevel admin

; Modern UI
!include "MUI2.nsh"

; Interface Settings
!define MUI_ABORTWARNING
!define MUI_ICON "assets\\icons\\app_icon.ico"
!define MUI_UNICON "assets\\icons\\app_icon.ico"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Languages
!insertmacro MUI_LANGUAGE "English"
!insertmacro MUI_LANGUAGE "Arabic"

; Installer sections
Section "Main Application" SecMain
    SetOutPath "$INSTDIR"
    
    ; Copy files
    File /r "dist\\Asphalto\\*"
    
    ; Create shortcuts
    CreateDirectory "$SMPROGRAMS\\${APP_NAME}"
    CreateShortcut "$SMPROGRAMS\\${APP_NAME}\\${APP_NAME}.lnk" "$INSTDIR\\Asphalto.exe"
    CreateShortcut "$DESKTOP\\${APP_NAME}.lnk" "$INSTDIR\\Asphalto.exe"
    
    ; Registry entries
    WriteRegStr HKLM "Software\\${APP_NAME}" "InstallDir" "$INSTDIR"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "DisplayName" "${APP_NAME}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "UninstallString" "$INSTDIR\\Uninstall.exe"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "Publisher" "${APP_PUBLISHER}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "DisplayVersion" "${APP_VERSION}"
    
    ; Create uninstaller
    WriteUninstaller "$INSTDIR\\Uninstall.exe"
SectionEnd

; Uninstaller section
Section "Uninstall"
    ; Remove files
    RMDir /r "$INSTDIR"
    
    ; Remove shortcuts
    Delete "$DESKTOP\\${APP_NAME}.lnk"
    RMDir /r "$SMPROGRAMS\\${APP_NAME}"
    
    ; Remove registry entries
    DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}"
    DeleteRegKey HKLM "Software\\${APP_NAME}"
SectionEnd
'''
    
    with open('asphalto_installer.nsi', 'w', encoding='utf-8') as f:
        f.write(nsis_script.strip())
    
    print("Created NSIS installer script: asphalto_installer.nsi")

def main():
    """Main build function"""
    print("🏗️  Building Asphalto Executable")
    print("=" * 50)
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print(f"✅ PyInstaller {PyInstaller.__version__} found")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
    
    # Clean previous builds
    print("\n1. Cleaning previous builds...")
    clean_build_dirs()
    
    # Create necessary files
    print("\n2. Creating build files...")
    create_version_info()
    create_pyinstaller_spec()
    
    # Build executable
    print("\n3. Building executable...")
    if build_executable():
        print("\n4. Creating installer script...")
        create_installer_script()
        
        print("\n🎉 Build completed successfully!")
        print("\nNext steps:")
        print("1. Test the executable in dist/Asphalto/")
        print("2. Create installer using NSIS (if on Windows)")
        print("3. Distribute the application")
        
        # Show final size
        exe_path = Path('dist/Asphalto/Asphalto.exe')
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"\nExecutable size: {size_mb:.1f} MB")
    else:
        print("\n❌ Build failed!")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
