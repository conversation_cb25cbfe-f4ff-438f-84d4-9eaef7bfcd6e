#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
About Dialog for Asphalto Application
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QFrame, QScrollArea,
                            QGroupBox, QTabWidget, QWidget)
from PyQt6.QtCore import Qt, QUrl
from PyQt6.QtGui import QFont, QPixmap, QDesktopServices

class AboutDialog(QDialog):
    """About dialog showing application information"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.init_ui()
    
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("حول البرنامج - Asphalto")
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        # Main layout
        layout = QVBoxLayout(self)
        
        # Create tab widget
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # About tab
        self.create_about_tab(tab_widget)
        
        # Developer tab
        self.create_developer_tab(tab_widget)
        
        # License tab
        self.create_license_tab(tab_widget)
        
        # Credits tab
        self.create_credits_tab(tab_widget)
        
        # Close button
        self.create_close_button(layout)
    
    def create_about_tab(self, tab_widget):
        """Create about tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Application logo and title
        title_frame = QFrame()
        title_layout = QVBoxLayout(title_frame)
        
        # App icon (placeholder)
        icon_label = QLabel("🏗️")
        icon_label.setFont(QFont("Arial", 48))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(icon_label)
        
        # App name
        app_name = QLabel("Asphalto")
        app_name.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        app_name.setAlignment(Qt.AlignmentFlag.AlignCenter)
        app_name.setStyleSheet("color: #2c3e50;")
        title_layout.addWidget(app_name)
        
        # App subtitle
        subtitle = QLabel("Professional Asphalt Pavement Thickness Calculator")
        subtitle.setFont(QFont("Arial", 12))
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle.setStyleSheet("color: #7f8c8d;")
        title_layout.addWidget(subtitle)
        
        # Arabic subtitle
        subtitle_ar = QLabel("برنامج احترافي لحساب سماكة طبقات الرصف الإسفلتي")
        subtitle_ar.setFont(QFont("Arial", 12))
        subtitle_ar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_ar.setStyleSheet("color: #7f8c8d;")
        title_layout.addWidget(subtitle_ar)
        
        layout.addWidget(title_frame)
        
        # Version info
        version_group = QGroupBox("معلومات الإصدار")
        version_layout = QVBoxLayout(version_group)
        
        version_info = QLabel("""
<b>الإصدار:</b> 1.0.0<br>
<b>تاريخ الإصدار:</b> 2025<br>
<b>نظام التصميم:</b> AASHTO 1993<br>
<b>المنصة:</b> Windows / Linux / macOS<br>
<b>اللغات المدعومة:</b> العربية، الإنجليزية
        """)
        version_info.setWordWrap(True)
        version_layout.addWidget(version_info)
        
        layout.addWidget(version_group)
        
        # Description
        desc_group = QGroupBox("وصف البرنامج")
        desc_layout = QVBoxLayout(desc_group)
        
        description = QLabel("""
يُعد Asphalto برنامجاً احترافياً متطوراً لحساب سماكة طبقات الرصف الإسفلتي وفقاً لمعايير AASHTO 1993. 
يوفر البرنامج واجهة مستخدم عصرية وسهلة الاستخدام مع ميزات متقدمة للتحليل والمحاكاة.

<b>الميزات الرئيسية:</b>
• حساب دقيق لسماكة الطبقات وفق AASHTO 1993
• تحليل الحساسية والتقييم الزمني
• مكتبة شاملة للمواد والترب
• تقارير احترافية بصيغة PDF
• دعم كامل للغة العربية والإنجليزية
• واجهة عصرية مع الوضع الداكن
• نظام إدارة المشاريع المتقدم
        """)
        description.setWordWrap(True)
        description.setAlignment(Qt.AlignmentFlag.AlignJustify)
        desc_layout.addWidget(description)
        
        layout.addWidget(desc_group)
        
        tab_widget.addTab(tab, "حول البرنامج")
    
    def create_developer_tab(self, tab_widget):
        """Create developer tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Developer info
        dev_group = QGroupBox("معلومات المطور")
        dev_layout = QVBoxLayout(dev_group)
        
        # Developer name
        dev_name = QLabel("المهندس محمد يونس الجوالي")
        dev_name.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        dev_name.setAlignment(Qt.AlignmentFlag.AlignCenter)
        dev_name.setStyleSheet("color: #2c3e50; padding: 10px;")
        dev_layout.addWidget(dev_name)
        
        # Developer title
        dev_title = QLabel("مهندس مدني - متخصص في هندسة الطرق والمواصلات")
        dev_title.setFont(QFont("Arial", 12))
        dev_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        dev_title.setStyleSheet("color: #7f8c8d;")
        dev_layout.addWidget(dev_title)
        
        layout.addWidget(dev_group)
        
        # Contact info
        contact_group = QGroupBox("معلومات التواصل")
        contact_layout = QVBoxLayout(contact_group)
        
        # WhatsApp
        whatsapp_frame = QFrame()
        whatsapp_layout = QHBoxLayout(whatsapp_frame)
        whatsapp_layout.addWidget(QLabel("📞 WhatsApp:"))
        
        whatsapp_link = QLabel('<a href="https://wa.me/9647507812241">+964 750 781 2241</a>')
        whatsapp_link.setOpenExternalLinks(True)
        whatsapp_link.setStyleSheet("color: #25D366;")
        whatsapp_layout.addWidget(whatsapp_link)
        whatsapp_layout.addStretch()
        
        contact_layout.addWidget(whatsapp_frame)
        
        # Facebook
        facebook_frame = QFrame()
        facebook_layout = QHBoxLayout(facebook_frame)
        facebook_layout.addWidget(QLabel("🌐 Facebook:"))
        
        facebook_link = QLabel('<a href="https://facebook.com/aljwalei">facebook.com/aljwalei</a>')
        facebook_link.setOpenExternalLinks(True)
        facebook_link.setStyleSheet("color: #1877F2;")
        facebook_layout.addWidget(facebook_link)
        facebook_layout.addStretch()
        
        contact_layout.addWidget(facebook_frame)
        
        # Instagram
        instagram_frame = QFrame()
        instagram_layout = QHBoxLayout(instagram_frame)
        instagram_layout.addWidget(QLabel("📸 Instagram:"))
        
        instagram_link = QLabel('<a href="https://instagram.com/m2n.9">instagram.com/m2n.9</a>')
        instagram_link.setOpenExternalLinks(True)
        instagram_link.setStyleSheet("color: #E4405F;")
        instagram_layout.addWidget(instagram_link)
        instagram_layout.addStretch()
        
        contact_layout.addWidget(instagram_frame)
        
        layout.addWidget(contact_group)
        
        # Professional background
        background_group = QGroupBox("الخلفية المهنية")
        background_layout = QVBoxLayout(background_group)
        
        background_text = QLabel("""
<b>التخصص:</b> هندسة مدنية - طرق ومواصلات<br>
<b>الخبرة:</b> تصميم وتنفيذ مشاريع الطرق والمطارات<br>
<b>الاهتمامات:</b> تطوير البرمجيات الهندسية، تحليل الرصف، تقنيات البناء الحديثة<br>
<b>المشاريع:</b> تطوير أدوات حاسوبية لتصميم الطرق والتحليل الهندسي
        """)
        background_text.setWordWrap(True)
        background_layout.addWidget(background_text)
        
        layout.addWidget(background_group)
        
        # Copyright notice
        copyright_frame = QFrame()
        copyright_frame.setFrameStyle(QFrame.Shape.Box)
        copyright_frame.setStyleSheet("background-color: #ecf0f1; padding: 10px;")
        copyright_layout = QVBoxLayout(copyright_frame)
        
        copyright_text = QLabel("جميع الحقوق محفوظة © 2025")
        copyright_text.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        copyright_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        copyright_layout.addWidget(copyright_text)
        
        layout.addWidget(copyright_frame)
        
        tab_widget.addTab(tab, "المطور")
    
    def create_license_tab(self, tab_widget):
        """Create license tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # License text
        license_text = QTextEdit()
        license_text.setReadOnly(True)
        license_text.setPlainText("""
ترخيص استخدام برنامج Asphalto
================================

حقوق الطبع والنشر © 2025 المهندس محمد يونس الجوالي

يُمنح هذا الترخيص للمستخدمين الحق في:

1. استخدام البرنامج لأغراض تعليمية وتجارية
2. إنشاء نسخ احتياطية للاستخدام الشخصي
3. توزيع البرنامج مع الحفاظ على معلومات حقوق الطبع والنشر

القيود:
- لا يُسمح بتعديل الكود المصدري دون إذن مكتوب
- لا يُسمح ببيع البرنامج أو أجزاء منه
- يجب الحفاظ على معلومات المطور في جميع النسخ

إخلاء المسؤولية:
يُقدم هذا البرنامج "كما هو" دون أي ضمانات صريحة أو ضمنية. 
لا يتحمل المطور أي مسؤولية عن أي أضرار قد تنتج عن استخدام البرنامج.

للحصول على ترخيص تجاري أو لأي استفسارات، يُرجى التواصل مع المطور.

تاريخ الترخيص: 2025
الإصدار: 1.0
        """)
        
        layout.addWidget(license_text)
        
        tab_widget.addTab(tab, "الترخيص")
    
    def create_credits_tab(self, tab_widget):
        """Create credits tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Third-party libraries
        libraries_group = QGroupBox("المكتبات المستخدمة")
        libraries_layout = QVBoxLayout(libraries_group)
        
        libraries_text = QLabel("""
يستخدم هذا البرنامج المكتبات مفتوحة المصدر التالية:

<b>PyQt6</b> - واجهة المستخدم الرسومية<br>
الترخيص: GPL v3<br>
الموقع: <a href="https://www.riverbankcomputing.com/software/pyqt/">riverbankcomputing.com</a><br><br>

<b>Matplotlib</b> - الرسوم البيانية والتصور<br>
الترخيص: PSF License<br>
الموقع: <a href="https://matplotlib.org/">matplotlib.org</a><br><br>

<b>NumPy</b> - الحوسبة العلمية<br>
الترخيص: BSD License<br>
الموقع: <a href="https://numpy.org/">numpy.org</a><br><br>

<b>SciPy</b> - الخوارزميات العلمية<br>
الترخيص: BSD License<br>
الموقع: <a href="https://scipy.org/">scipy.org</a><br><br>

<b>Pandas</b> - تحليل البيانات<br>
الترخيص: BSD License<br>
الموقع: <a href="https://pandas.pydata.org/">pandas.pydata.org</a><br><br>

<b>ReportLab</b> - إنشاء ملفات PDF<br>
الترخيص: BSD License<br>
الموقع: <a href="https://www.reportlab.com/">reportlab.com</a>
        """)
        libraries_text.setWordWrap(True)
        libraries_text.setOpenExternalLinks(True)
        libraries_layout.addWidget(libraries_text)
        
        layout.addWidget(libraries_group)
        
        # Acknowledgments
        ack_group = QGroupBox("شكر وتقدير")
        ack_layout = QVBoxLayout(ack_group)
        
        ack_text = QLabel("""
نتقدم بالشكر والتقدير إلى:

• <b>AASHTO</b> لتطوير معايير تصميم الرصف المستخدمة في البرنامج
• <b>مجتمع Python</b> لتوفير أدوات التطوير الممتازة
• <b>مجتمع المطورين مفتوحي المصدر</b> لمساهماتهم القيمة
• <b>المهندسين والأكاديميين</b> الذين قدموا الملاحظات والاقتراحات
• <b>جامعات الهندسة المدنية</b> لتوفير المراجع العلمية

شكر خاص للزملاء المهندسين الذين ساعدوا في اختبار البرنامج وتطويره.
        """)
        ack_text.setWordWrap(True)
        ack_layout.addWidget(ack_text)
        
        layout.addWidget(ack_group)
        
        tab_widget.addTab(tab, "شكر وتقدير")
    
    def create_close_button(self, layout):
        """Create close button"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = QPushButton("إغلاق")
        close_btn.setMinimumWidth(100)
        close_btn.setDefault(True)
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
