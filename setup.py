#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Setup script for Asphalto application
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="asphalto",
    version="1.0.0",
    author="<PERSON>",
    author_email="<EMAIL>",
    description="Professional Asphalt Pavement Thickness Calculator based on AASHTO 1993",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/aljwalei/asphalto",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Science/Research",
        "Intended Audience :: Education",
        "Topic :: Scientific/Engineering",
        "Topic :: Scientific/Engineering :: Physics",
        "License :: Other/Proprietary License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
        "Natural Language :: Arabic",
        "Natural Language :: English",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-qt>=4.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.950",
        ],
        "docs": [
            "sphinx>=4.0.0",
            "sphinx-rtd-theme>=1.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "asphalto=main:main",
        ],
        "gui_scripts": [
            "asphalto-gui=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "src.data": ["*.json"],
        "assets": ["icons/*", "images/*"],
        "translations": ["*.qm", "*.ts"],
    },
    data_files=[
        ("share/asphalto/assets/icons", ["assets/icons/app_icon.png"]),
        ("share/asphalto/docs", ["README.md"]),
    ],
    zip_safe=False,
    keywords=[
        "pavement", "asphalt", "AASHTO", "civil engineering", "road design",
        "structural number", "thickness calculation", "transportation engineering"
    ],
    project_urls={
        "Bug Reports": "https://github.com/aljwalei/asphalto/issues",
        "Source": "https://github.com/aljwalei/asphalto",
        "Documentation": "https://asphalto.readthedocs.io/",
        "Developer": "https://facebook.com/aljwalei",
    },
)
