#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Install requirements for Asphalto application
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a single package using pip"""
    try:
        print(f"Installing {package}...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', package
        ], capture_output=True, text=True, check=True)
        print(f"✅ {package} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        print(f"Error output: {e.stderr}")
        return False

def main():
    """Install all requirements"""
    print("🔧 Installing Asphalto Requirements")
    print("=" * 50)
    
    # Check if requirements.txt exists
    if not os.path.exists('requirements.txt'):
        print("❌ requirements.txt not found!")
        return 1
    
    # Read requirements
    with open('requirements.txt', 'r') as f:
        packages = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    
    print(f"Found {len(packages)} packages to install:")
    for package in packages:
        print(f"  - {package}")
    
    print("\nStarting installation...")
    
    # Install packages
    failed_packages = []
    for package in packages:
        if not install_package(package):
            failed_packages.append(package)
    
    print("\n" + "=" * 50)
    if failed_packages:
        print(f"❌ {len(failed_packages)} packages failed to install:")
        for package in failed_packages:
            print(f"  - {package}")
        print("\nTry installing them manually:")
        for package in failed_packages:
            print(f"  pip install {package}")
        return 1
    else:
        print("🎉 All packages installed successfully!")
        print("\nYou can now run the application:")
        print("  python main.py")
        return 0

if __name__ == '__main__':
    sys.exit(main())
