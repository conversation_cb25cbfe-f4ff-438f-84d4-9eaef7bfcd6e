import { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import { PDFDocument, StandardFonts } from 'pdf-lib';

interface LayerResult {
  name: string;
  thickness: number;
}

const Results = () => {
  const [results] = useState<LayerResult[]>([
    { name: 'Surface Course', thickness: 5 },
    { name: 'Binder Course', thickness: 7 },
    { name: 'Base Course', thickness: 15 },
  ]);

  const generatePDF = async () => {
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    
    page.drawText('Pavement Design Results', {
      x: 50,
      y: page.getHeight() - 50,
      size: 20,
      font,
    });

    // Add more PDF content here
    
    const pdfBytes = await pdfDoc.save();
    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);
    window.open(url);
  };

  return (
    <Paper sx={{ p: 3, mt: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Results
        </Typography>
        <Button variant="contained" onClick={generatePDF}>
          Generate PDF Report
        </Button>
      </Box>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Layer</TableCell>
              <TableCell align="right">Thickness (cm)</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {results.map((row) => (
              <TableRow key={row.name}>
                <TableCell component="th" scope="row">
                  {row.name}
                </TableCell>
                <TableCell align="right">{row.thickness}</TableCell>
              </TableRow>
            ))}
            <TableRow>
              <TableCell><strong>Total Thickness</strong></TableCell>
              <TableCell align="right">
                <strong>
                  {results.reduce((sum, layer) => sum + layer.thickness, 0)}
                </strong>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

export default Results;
