import { useState } from 'react';
import {
  Box,
  TextField,
  Grid,
  Paper,
  IconButton,
  Tooltip,
  Typography,
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { useTranslation } from 'react-i18next';

interface InputField {
  name: string;
  label: string;
  helpText: string;
  min?: number;
  max?: number;
}

const InputForm = () => {
  const { t } = useTranslation();
  const [values, setValues] = useState({
    w18: '',
    psi: '',
    zr: '',
    s0: '',
    cbr: '',
    eValues: '',
    designPeriod: '',
  });

  const inputFields: InputField[] = [
    {
      name: 'w18',
      label: t('inputs.w18'),
      helpText: 'Number of 18-kip (80 kN) single axle load applications',
    },
    {
      name: 'psi',
      label: t('inputs.psi'),
      helpText: 'Difference between initial and terminal serviceability',
      min: 0,
      max: 5,
    },
    // Add more fields as needed
  ];

  const handleChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setValues({ ...values, [field]: event.target.value });
  };

  return (
    <Paper sx={{ p: 3, mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Input Parameters
      </Typography>
      <Grid container spacing={3}>
        {inputFields.map((field) => (
          <Grid item xs={12} sm={6} key={field.name}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TextField
                fullWidth
                label={field.label}
                value={values[field.name as keyof typeof values]}
                onChange={handleChange(field.name)}
                type="number"
                InputProps={{
                  inputProps: {
                    min: field.min,
                    max: field.max,
                  },
                }}
              />
              <Tooltip title={field.helpText}>
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpOutlineIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

export default InputForm;
